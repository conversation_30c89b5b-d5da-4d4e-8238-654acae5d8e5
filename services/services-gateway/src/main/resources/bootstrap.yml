nacos_addr: 127.0.0.1:8848
nacos_name_space: v-linker-v2
spring:
  mvc:
    async:
      request-timeout: 86400000
  profiles:
    active: test
  application:
    name: gateway-server
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_addr}
        namespace: ${nacos_name_space}
        fail-fast: false
      config:
        server-addr: ${nacos_addr}
        namespace: ${nacos_name_space}
        file-extension: yml
        shared-configs:
          - data-id: shared-common.yml
          - data-id: shared-redis.yml
          - data-id: shared-s3.yml
          - data-id: shared-network.yml