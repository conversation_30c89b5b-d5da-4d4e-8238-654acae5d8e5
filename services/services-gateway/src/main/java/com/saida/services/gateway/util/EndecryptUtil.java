package com.saida.services.gateway.util;

import com.saida.services.constant.VarConstants;
import com.saida.services.entities.pojo.EncryptedData;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.result.ResultCode;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

public class EndecryptUtil {

    public static String decryptReq(Map<String, List<String>> formdata) throws BizRuntimeException {
        if (formdata.get("encryptedData").get(0) == null || formdata.get("encryptedData").get(0).length() == 0) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无数据)!");
        }
        String encryptedData = formdata.get("encryptedData").get(0);
        if (formdata.get("sign").get(0) == null || formdata.get("sign").get(0).length() == 0) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无签名)!");
        }
        String sign = formdata.get("sign").get(0);
        if (formdata.get("timestamp").get(0) == null || formdata.get("timestamp").get(0).length() == 0) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无时间)!");
        }
        String timestampstr = formdata.get("timestamp").get(0);
        long timestamp = Long.parseLong(timestampstr);
        String checksign = CodecUtil.sha1Encrypt(encryptedData + timestampstr);
        if (!checksign.equals(sign)) {
            throw new BizRuntimeException("签名错误!");
        }
        if (((System.currentTimeMillis() - timestamp) / 1000) > VarConstants.TIMESTAMP_EXPIRE) {
            throw new BizRuntimeException("时间校验错误! 请保持和服务器同步");
        }
        return CodecUtil.aesDecrypt(encryptedData);
    }



    public static String decryptReq(EncryptedData formdata) throws BizRuntimeException {
        if (formdata == null) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(data是空的)!");
        }
        if (StringUtils.isEmpty(formdata.getSign())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无签名)!");
        }
        if (StringUtils.isEmpty(formdata.getTimestamp())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无时间)!");
        }
        if (StringUtils.isEmpty(formdata.getEncryptedData())) {
            throw new BizRuntimeException(ResultCode.BAD_REQUEST_PARAM.getCode(), "缺少必要参数(无数据)!");
        }
        long timestamp = Long.parseLong(formdata.getTimestamp());
        String checksign = CodecUtil.sha1Encrypt(formdata.getEncryptedData() + formdata.getTimestamp());
        if (!checksign.equals(formdata.getSign())) {
            throw new BizRuntimeException("签名错误!");
        }
        if (((System.currentTimeMillis() - timestamp) / 1000) > VarConstants.TIMESTAMP_EXPIRE) {
            throw new BizRuntimeException("时间校验错误! 请保持和服务器同步");
        }
        return CodecUtil.aesDecrypt(formdata.getEncryptedData());
    }
}
