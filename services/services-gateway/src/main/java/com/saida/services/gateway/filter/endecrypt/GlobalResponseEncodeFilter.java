package com.saida.services.gateway.filter.endecrypt;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.gateway.config.CspConfig;
import com.saida.services.gateway.util.CodecUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "sysconfig", name = "enableEndecrypt", havingValue = "1")
public class GlobalResponseEncodeFilter implements GlobalFilter, Ordered {

    @Autowired
    private FilterUrlConfig filterUrlConfig;

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    //是否开启了加密
    @Autowired(required = false)
    private CspConfig cspConfig;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        for (String pattern : filterUrlConfig.getResponse()) {
            if (StringUtils.isEmpty(pattern)) {
                continue;
            }
            if (pathMatcher.match(pattern, request.getPath().value())) {
                return chain.filter(exchange);
            }
        }

        String cspServer = request.getHeaders().getFirst("Certification-version");
        String cspSign = request.getHeaders().getFirst("Certification-sign");


        DataBufferFactory bufferFactory = exchange.getResponse().bufferFactory();

        ServerHttpResponseDecorator responseDecorator = new ServerHttpResponseDecorator(exchange.getResponse()) {
            @Override
            @NonNull
            public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                    // ✅ 用 DataBufferUtils.join 保证合并完整
                    return DataBufferUtils.join(fluxBody)
                            .flatMap(dataBuffer -> {
                                try {
                                    byte[] content = new byte[dataBuffer.readableByteCount()];
                                    dataBuffer.read(content);
                                    JSONObject result = new JSONObject();
                                    result.put("code", 201);
                                    if ("csp".equals(cspServer)) {
                                        result.put("code", 201);
                                        String responseData = new String(content, StandardCharsets.UTF_8);
                                        result.put("data", cspConfig.encryptResp(responseData, cspSign));
                                    }else if ("csp".equals(cspServer)) {
                                        result.put("code", 202);
                                        result.put("data", CodecUtil.aesEncrypt(content));
                                    } else {
                                        result.put("code", 201);
                                        if (containsUtf8Replacement(content)) {
                                            log.error("上游原始响应已包含 U+FFFD（EF BF BD），源端编码已损坏，网关只做透传加密。");
                                        }
                                        result.put("data", CodecUtil.aesEncrypt(content));
                                    }
                                    byte[] newContent = result.toJSONString().getBytes(StandardCharsets.UTF_8);
                                    getDelegate().getHeaders().setContentLength(newContent.length);
                                    DataBuffer buffer = bufferFactory.wrap(newContent);
                                    return super.writeWith(Mono.just(buffer));
                                } finally {
                                    DataBufferUtils.release(dataBuffer); // ✅ 释放内存
                                }
                            });
                }
                return super.writeWith(body);
            }
        };
        return chain.filter(exchange.mutate().response(responseDecorator).build());
    }

    private static boolean containsUtf8Replacement(byte[] bytes) {
        for (int i = 0; i + 2 < bytes.length; i++) {
            if ((bytes[i] & 0xFF) == 0xEF && (bytes[i + 1] & 0xFF) == 0xBF && (bytes[i + 2] & 0xFF) == 0xBD) {
                return true;
            }
        }
        return false;
    }


    @Override
    public int getOrder() {
        return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 1;
    }
}