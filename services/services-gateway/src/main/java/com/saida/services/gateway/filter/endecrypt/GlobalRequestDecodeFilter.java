package com.saida.services.gateway.filter.endecrypt;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.entities.pojo.EncryptedData;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.gateway.config.CspConfig;
import com.saida.services.gateway.processor.ErrorCodeException;
import com.saida.services.gateway.util.EndecryptUtil;
import com.saida.services.gateway.util.GlobalVar;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.PathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "sysconfig", name = "enableEndecrypt", havingValue = "1")
public class GlobalRequestDecodeFilter implements GlobalFilter, Ordered {

    @Resource
    private FilterUrlConfig filterUrlConfig;

    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private final DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 是否开启了加密
    @Autowired(required = false)
    private CspConfig cspConfig;

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        GatewayContext gatewayContext = GatewayContext.getGatewayContext(exchange);

        MediaType contentType = request.getHeaders().getContentType();
        if (GlobalVar.autoTokenPath.equals(request.getPath().value())) {
            gatewayContext.setIgnoreDecode(true);
        }
        String cspServer = request.getHeaders().getFirst("Certification-version");
        if (filterUrlConfig != null && filterUrlConfig.getRequest() != null && !filterUrlConfig.getRequest().isEmpty()) {
            for (String pattern : filterUrlConfig.getRequest()) {
                if (StringUtils.isEmpty(pattern)) {
                    continue;
                }
                if (pathMatcher.match(pattern, request.getPath().value())) {
                    gatewayContext.setIgnoreDecode(true);
                }
            }
        }
        switch (request.getMethodValue().toUpperCase()) {
            case "GET":
                if (gatewayContext.isIgnoreDecode()) {
                    gatewayContext.setParam(objectMapper.readValue(JSON.toJSONString(request.getQueryParams()), new TypeReference<Map<String, Object>>() {
                    }));
                    return chain.filter(exchange);
                }
                String decryptGetData;
                if ("csp".equals(cspServer)) {
                    if (cspConfig == null) {
                        throw new BizRuntimeException("未开启量子加密！");
                    }
                    decryptGetData = cspConfig.decryptReq(request.getQueryParams());
                } else {
                    decryptGetData = EndecryptUtil.decryptReq(request.getQueryParams());
                }
                if (StringUtils.isEmpty(decryptGetData)) {
                    throw new BizRuntimeException("参数错误");
                }

                URI uri = request.getURI();
                // URI newUri = new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), queryStringBuilder.toString(), null);

                URIBuilder builder = new URIBuilder();
                builder.setScheme(uri.getScheme())
                        .setHost(uri.getHost())
                        .setPath(uri.getPath());

                JSONObject jsonObject = JSON.parseObject(decryptGetData);
                if (jsonObject != null && !jsonObject.isEmpty()) {
                    for (String key : jsonObject.keySet()) {
                        Object o = jsonObject.get(key);
                        if (o != null) {
                            String value = String.valueOf(o);
                            value = value.replaceAll("=", URLUtil.encode("=", StandardCharsets.UTF_8));
                            builder.addParameter(key, value);
                        } else {
                            builder.addParameter(key, null);
                        }
                    }
                }
                URI newUri = builder.build();

                gatewayContext.setParam(objectMapper.readValue(decryptGetData, new TypeReference<Map<String, Object>>() {
                }));
                return chain.filter(exchange.mutate().request(request.mutate().uri(newUri).build()).build());
            case "POST":
                if (contentType == null) {
                    throw new BizRuntimeException("请求信息错误");
                }
                String contentTypeHeader = contentType.toString();
                if (!contentTypeHeader.startsWith(MediaType.APPLICATION_JSON_VALUE) &&
                        !contentTypeHeader.startsWith(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                    return chain.filter(exchange);
                }
                return request.getBody().buffer().map((Function<? super List<DataBuffer>, ? extends Mono<Object>>) dataBuffers -> {
                    DataBuffer joinDataBuffer = dataBufferFactory.join(dataBuffers);
                    byte[] reqData = new byte[joinDataBuffer.readableByteCount()];
                    joinDataBuffer.read(reqData);
                    DataBufferUtils.release(joinDataBuffer);
                    GatewayContext gContext = GatewayContext.getGatewayContext(exchange);
                    String encryptData = new String(reqData, StandardCharsets.UTF_8);
                    String decryptData = "";
                    if (contentTypeHeader.startsWith(MediaType.APPLICATION_JSON_VALUE)) {
                        try {
                            if (gContext.isIgnoreDecode()) {
                                gContext.setParam(objectMapper.readValue(encryptData, new TypeReference<Map<String, Object>>() {
                                }));
                            } else {
                                if ("csp".equals(cspServer)) {
                                    decryptData = cspConfig.decryptReq(JSON.parseObject(encryptData, EncryptedData.class));
                                } else {
                                    decryptData = EndecryptUtil.decryptReq(JSON.parseObject(encryptData, EncryptedData.class));
                                }
                                gContext.setParam(objectMapper.readValue(decryptData, new TypeReference<Map<String, Object>>() {
                                }));
                            }
                        } catch (BizRuntimeException | JsonProcessingException e) {
                            log.error("GlobalRequestDecodeFilter->{}", e.getMessage(), e);
                            throw new ErrorCodeException(e.getMessage());
                        }
                    } else if (contentTypeHeader.startsWith(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                        MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
                        String[] keyValuePairs = encryptData.split("&");
                        for (String keyValue : keyValuePairs) {
                            String[] pair = keyValue.split("=");
                            if (pair.length == 2) {
                                String key = pair[0];
                                String value = URLUtil.decode(pair[1], StandardCharsets.UTF_8);
                                param.add(key, value);
                            }
                        }
                        try {
                            if (gContext.isIgnoreDecode()) {
                                gContext.setParam(objectMapper.readValue(JSON.toJSONString(param), new TypeReference<Map<String, Object>>() {
                                }));
                            } else {
                                if ("csp".equals(cspServer)) {
                                    decryptData = cspConfig.decryptReq(param);
                                } else {
                                    decryptData = EndecryptUtil.decryptReq(param);
                                }
                                gContext.setParam(objectMapper.readValue(decryptData, new TypeReference<Map<String, Object>>() {
                                }));
                                decryptData = UrlQuery.of(objectMapper.readValue(decryptData, new TypeReference<Map<String, Object>>() {
                                }), true).build(CharsetUtil.CHARSET_UTF_8);
                            }
                        } catch (BizRuntimeException | JsonProcessingException e) {
                            log.error("解密失败！：{}", e.getMessage(), e);
                            throw new ErrorCodeException(e.getMessage());
                        }
                    }

                    if (!gContext.isIgnoreDecode()) {
                        gContext.setReqData(decryptData.getBytes(StandardCharsets.UTF_8));
                    } else {
                        gContext.setReqData(encryptData.getBytes(StandardCharsets.UTF_8));
                    }
                    return Mono.empty();
                }).then(Mono.defer(() -> {
                    GatewayContext gContext = GatewayContext.getGatewayContext(exchange);
                    ServerHttpRequestDecorator requestDecorator = new ServerHttpRequestDecorator(request) {
                        @Override
                        public HttpHeaders getHeaders() {
                            HttpHeaders httpHeaders = new HttpHeaders();
                            httpHeaders.putAll(super.getHeaders());
                            httpHeaders.remove(HttpHeaders.CONTENT_LENGTH);
                            httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                            return httpHeaders;
                        }

                        @Override
                        public Flux<DataBuffer> getBody() {
                            byte[] datebytes = gContext.getReqData();
                            gContext.setReqData(null);
                            if (datebytes == null) {
                                datebytes = new byte[0];
                            }
                            return Flux.just(dataBufferFactory.wrap(Objects.requireNonNull(datebytes)));
                        }
                    };
                    return chain.filter(exchange.mutate().request(requestDecorator).build());
                }));
            default:
                return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }
}
