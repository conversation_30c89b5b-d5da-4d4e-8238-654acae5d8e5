package com.saida.services.gateway.processor.adapter.common;

import cn.hutool.core.collection.CollectionUtil;
import com.saida.services.gateway.filter.endecrypt.GatewayContext;
import com.saida.services.gateway.processor.BaseAdapter;
import com.saida.services.gateway.processor.ProcessorContext;
import com.saida.services.gateway.util.IPUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RequestLogAdapter extends BaseAdapter {

    @Override
    public void process(ProcessorContext context) {
        try {
            ServerWebExchange exchange = context.getExchange();
            GatewayContext gatewayContext = GatewayContext.getGatewayContext(exchange);
            ServerHttpRequest request = exchange.getRequest();

            log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 请求日志打印开始 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
            log.info("请求PATH ：【{}】", context.getRouteId());
            log.info("请求方式  ：【{}】", null == request.getMethod() ? null : request.getMethod().name());
            log.info("reqId    ：【{}】", this.getRequestId(request));
            log.info("请求头部  ：【{}】", this.getContentType(request));
            log.info("用户IP   ：【{}】", IPUtil.getClientIP(exchange));
            log.info("用户ID   ：【{}】", gatewayContext.getUserId());
            log.info("用户名   ：【{}】", gatewayContext.getUserName());
            log.info("账  号   ：【{}】", gatewayContext.getAccount());
            HttpHeaders headers = request.getHeaders();
            log.info("user-agent   ：【{}】", headers.getFirst("user-agent"));

            // SEC 前缀
            StringBuilder secSb = new StringBuilder();
            StringBuilder vlinkerSb = new StringBuilder();
            for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                String key = entry.getKey();
                String value = String.join(",", entry.getValue());
                if (key.regionMatches(true, 0, "sec-", 0, 4)) {
                    secSb.append(key).append('=').append(value).append(" | ");
                } else if (key.regionMatches(true, 0, "v-linker-", 0, 8)) {
                    vlinkerSb.append(key).append('=').append(value).append(" | ");
                }
            }
            if (secSb.length() > 0) {
                log.info("请求头 [SEC] : {}", secSb);
            }
            if (vlinkerSb.length() > 0) {
                log.info("请求头 [V-LINKER] : {}", vlinkerSb);
            }


//            log.info("参  数   ：【{}】", JSON.toJSON(context.getParameters().entrySet()));
            log.info("请求参数 Parameters：");
            for (Map.Entry<String, Object> map : context.getParameters().entrySet()) {
                String value = map.getValue() != null ? String.valueOf(map.getValue()) : "";
                if (value.length() > 1000) {
                    value = "len{" + value.length() + "}";
                }
                log.info("   参数名:【{}】,参数值:【{}】", map.getKey(), value);
            }
            log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 请求日志打印结束 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        } catch (Exception e) {
            log.error("RequestLogAdapter process error...msg={}", e.getMessage(), e);
        }
    }

    @Override
    public String getRouteRule() {
        return "/**";
    }

    private String getContentType(ServerHttpRequest request) {
        // 获取Content-Type头信息
        HttpHeaders headers = request.getHeaders();
        List<String> contentTypeHeaders = headers.get("Content-Type");
        return CollectionUtil.isEmpty(contentTypeHeaders) ? null : contentTypeHeaders.get(0);
    }

    private String getRequestId(ServerHttpRequest request) {
        // 获取Content-Type头信息
        HttpHeaders headers = request.getHeaders();
        List<String> requestIdHeaders = headers.get("Request-Id");
        return CollectionUtil.isEmpty(requestIdHeaders) ? null : requestIdHeaders.get(0);
    }
}
