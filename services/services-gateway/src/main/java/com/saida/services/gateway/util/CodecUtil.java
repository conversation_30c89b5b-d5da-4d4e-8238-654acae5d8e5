package com.saida.services.gateway.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 加解密工具类
 */
@Slf4j
public class CodecUtil {
    private CodecUtil() {

    }

    /**
     * AES加密密钥
     */
    private static final byte[] AES_SECRET_KEY_BYTES = Base64Utils.decodeFromString("QUBKZlojeGlwakJCc0RpcA==");

    /**
     * SHA1加密密钥（用于增加加密的复杂度）
     */
    public static final String SHA1_SECRET_KEY = "MDNkZCEyekZTciN6S2dPUA==";
    /**
     * AES 加密方式
     */
    public static final String AES_ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * 对数据进行加密，用AES加密再用Base64编码
     *
     * @param data 待加密数据
     */
    public static String aesEncrypt(String data) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM); // 加密算法/工作模式/填充方式
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(AES_SECRET_KEY_BYTES, "AES"));
            byte[] result = cipher.doFinal(dataBytes);
            return Base64Utils.encodeToString(result);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesEncrypt失败：data={}，异常：{}", data, e.getMessage(), e);
        }
        return null;
    }
    /**
     * 对数据进行加密，用AES加密再用Base64编码
     *
     * @param dataBytes 待加密数据
     */
    public static String aesEncrypt(byte[] dataBytes) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM); // 加密算法/工作模式/填充方式
//            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(AES_SECRET_KEY_BYTES, "AES"));
            byte[] result = cipher.doFinal(dataBytes);
            return Base64Utils.encodeToString(result);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesEncrypt失败：data={}，异常：{}", new String(dataBytes, StandardCharsets.UTF_8), e.getMessage(), e);
        }
        return null;
    }

    /**
     * 对数据进行加密，用AES解密
     */
    public static String aesDecrypt(String encryptedDataBase64) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM); // 加密算法/工作模式/填充方式
            byte[] dataBytes = Base64Utils.decodeFromString(encryptedDataBase64);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(AES_SECRET_KEY_BYTES, "AES"));
            byte[] result = cipher.doFinal(dataBytes);
            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesDecrypt失败：data={}，异常：{}", encryptedDataBase64, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 对数据进行加密，用SHA1加密再转换为16进制
     */
    public static String sha1Encrypt(String data) {
        return DigestUtils.sha1Hex(data + SHA1_SECRET_KEY);
    }

    public static void main(String[] args) {
        //fa7ebc2b745ad2448e281e3dbb6a3598c972f356
        //2ywG7lrpQhIMgRExQZt5EYHbAvV07DcH5SkUOfP0SvE+XnYvQ/J9KqxvOi18+cL++4jd5tKXxgvD5l2duhDD4HLIjrC7vk3LqqSMjyY+4KlvlnUC0ZwlJLBU+Sg/+iGQt2hsfclRn6fPqolbpF1JfA==
        //2ywG7lrpQhIMgRExQZt5EYHbAvV07DcH5SkUOfP0SvE XnYvQ/J9KqxvOi18 cL  4jd5tKXxgvD5l2duhDD4HLIjrC7vk3LqqSMjyY 4KlvlnUC0ZwlJLBU Sg/ iGQt2hsfclRn6fPqolbpF1JfA==
        //1745831458881
        //4a7986e576dc3f2b1396051d7062f81b6187d01f
//        String a = "{\"networking\":\"1\",\"protocol\":\"SD_URL\",\"deviceId\":\"1915294392838754306\",\"keepalive\":\"true\",\"bitstream\":\"1\"}";
//        String s = aesEncrypt(a);
//        System.out.println(s);
//        String s1 = CodecUtil.sha1Encrypt("2ywG7lrpQhIMgRExQZt5EYHbAvV07DcH5SkUOfP0SvE XnYvQ/J9KqxvOi18 cL  4jd5tKXxgvD5l2duhDD4HLIjrC7vk3LqqSMjyY 4KlvlnUC0ZwlJLBU Sg/ iGQt2hsfclRn6fPqolbpF1JfA==" + "1745831126843");
//        System.out.println(s1);

//        select concat('|',DEVICE_NAME,'|') c1, concat('|',CHANNEL_NAME,'|') c2 from VIDEO_SCENARIO_SYSTEM.VISUAL_DEVICE where DEVICE_NAME like '石%';
        byte[] bytes = "石狗���".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "石狗镇".getBytes(StandardCharsets.UTF_8);

        System.out.println(Arrays.equals(bytes, bytes2));

//        System.out.println(aesDecrypt("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"));
    }
}
