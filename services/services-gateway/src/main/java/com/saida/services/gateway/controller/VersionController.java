package com.saida.services.gateway.controller;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.gateway.config.GitPropertiesConfig;
import com.saida.services.gateway.config.ProjectConfig;
import com.saida.services.gateway.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/")
public class VersionController {
    @Autowired
    private ProjectConfig projectConfig;
    @Autowired(required = false)
    private GitPropertiesConfig gitPropertiesConfig;

    @GetMapping("v")
    public Result<JSONObject> version() {
        JSONObject data = new JSONObject();
        data.put("config", projectConfig);
        if (gitPropertiesConfig != null) {
            data.put("commitId", gitPropertiesConfig.getCommitId());
            data.put("commitCount", gitPropertiesConfig.getClosestTagCommitCount());
        }
        return Result.success(data);
    }

    @GetMapping("")
    public String index() {
        return "Vlinker？你再找什么呢";
    }

    @GetMapping("favicon.ico")
    public void favicon() {
    }


}
