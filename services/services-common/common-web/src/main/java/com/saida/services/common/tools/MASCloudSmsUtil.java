package com.saida.services.common.tools;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONObject;
import com.saida.services.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 四会  MAS短信平台对接
 * 账号来源 四会市信息中心林主任 登录  http://mas.10086.cn/login
 */
@Slf4j
public class MASCloudSmsUtil {

    private static final String PATH = "http://112.35.1.155:1992/sms/norsubmit";

    /**
     * 企业名称
     */
    private static final String EC_NAME = "四会市信息中心";

    /**
     * 账号用户名
     */
    private static final String APID = "vlinker";

    /**
     * 密钥
     */
    private static final String SECRET_KEY = "aYCVrvTGjpA.2025";

    /**
     * 签名编码  【四会政务服务平台】
     */
    private static final String SIGN = "e1Ai1DzIQ";

    /**
     * 扩展码
     */
    private static final String ADD_SERIAL = "";

    /**
     * 发送短信
     *
     * @param phone   接收短信的手机号
     * @param content 短信内容
     */
    public static String sendSms(String phone, String content) {
        log.info("MAS发送短信: phone:{},content:{}", phone, content);

        // 短信请求内容构造
        JSONObject json = new JSONObject();
        json.put("ecName", EC_NAME);
        json.put("apId", APID);
        json.put("mobiles", phone);
        json.put("content", content);
        json.put("sign", SIGN);
        json.put("addSerial", "");
        json.put("mac", DigestUtil.md5Hex(EC_NAME + APID + SECRET_KEY + phone + content + SIGN + ADD_SERIAL));

        HttpRequest request = HttpRequest.post(PATH)
                .body(Base64.encode(json.toJSONString(), StandardCharsets.UTF_8));
        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                log.error("MAS发送短信失败，接口响应非200，请求：{} 响应: {}", request, response);
                throw new BizRuntimeException("发送短信失败");
            }
            JSONObject responseObject = JSONObject.parseObject(response.body());
            if (!StringUtil.equals(responseObject.getString("rspcod"), "success")) {
                log.error("MAS发送短信失败，接口返回非success，请求：{} 响应: {}", request, response);
                throw new BizRuntimeException("发送短信失败");
            }
            // mgsGroup消息批次号，由云MAS平台生成，用于关联短信发送请求与状态报告，注：若数据验证不通过，该参数值为空。
            return responseObject.getString("mgsGroup");
        } catch (Exception e) {
            log.error("MAS发送短信异常", e);
            throw new BizRuntimeException("发送短信失败");
        }
    }

    public static void main(String[] args) {
        sendSms("15821265205", "测试短信");
    }

}
