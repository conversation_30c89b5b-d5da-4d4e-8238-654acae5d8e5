package com.saida.services.common.config.oss.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.SdkClientException;
import com.saida.services.common.config.oss.OSSBean;
import com.saida.services.common.config.oss.OSSMethodInterface;
import com.saida.services.common.config.oss.S3ObjectDto;
import com.saida.services.common.config.oss.S3Rule;
import com.saida.services.common.tools.ContentTypeUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.common.tools.VlinkerThrowableUtil;
import com.saida.services.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.SdkHttpResponse;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Service("defS3Impl")
public class DefS3Impl implements OSSMethodInterface {

    @Override
    public S3ObjectDto listObjects(OSSBean ossBean, String delimiter, String prefix, String nextContinuationToken) {
        S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
        ListObjectsRequest request = ListObjectsRequest.builder()
                .bucket(ossBean.getBucket())
                .delimiter(delimiter)
                .marker(StringUtil.isEmpty(nextContinuationToken) ? null : nextContinuationToken)
                .prefix(StringUtil.isEmpty(prefix) ? null : prefix)
                .build();
        ListObjectsResponse listObjectsResponse = null;
        try {
            log.info("listObjects ->  endPoint:{} ,bucket:{} ,delimiter:{} ,prefix:{} ,marker:{}", ossBean.getEndPoint(), request.bucket(), request.delimiter(), request.prefix(), request.marker());
            listObjectsResponse = s3Client.listObjects(request);
        } catch (Exception e) {
            log.error("listObjects -> err {}", e.getMessage(), e);
            throw new RuntimeException("云存查询失败！");
        }
        List<S3Object> contents = listObjectsResponse.contents();
        String s = listObjectsResponse.nextMarker();
        List<CommonPrefix> commonPrefixes = listObjectsResponse.commonPrefixes();
        List<S3ObjectDto.ObjectDto> list = new ArrayList<>();
        commonPrefixes.forEach(e -> {
            S3ObjectDto.ObjectDto dto = new S3ObjectDto.ObjectDto();
            dto.setType(1);
            dto.setKey(e.prefix());
            String[] split = e.prefix().split("/");
            if (split.length > 1) {
                dto.setName(split[split.length - 1]);
            } else if (split.length == 1) {
                dto.setName(split[0]);
            } else {
                dto.setName(e.prefix());
            }
            list.add(dto);
        });
        contents.forEach(e -> {
            S3ObjectDto.ObjectDto dto = new S3ObjectDto.ObjectDto();
            dto.setType(2);
            dto.setKey(e.key());
            String[] split = e.key().split("/");
            if (split.length > 1) {
                dto.setName(split[split.length - 1]);
            } else {
                dto.setName(e.key());
            }
            dto.setSize(e.size());
            dto.setLastModified(DateUtil.format(Date.from(e.lastModified()), DatePattern.NORM_DATETIME_PATTERN));
            list.add(dto);
        });
        S3ObjectDto res = new S3ObjectDto();
        res.setContents(list);
        res.setNextContinuationToken(s);
        return res;
    }

    @Override
    public S3ObjectDto listObjectsAndRule(OSSBean ossBean, String delimiter, String prefix, String nextContinuationToken) {
        S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
        ListObjectsRequest request = ListObjectsRequest.builder()
                .bucket(ossBean.getBucket())
                .delimiter(delimiter)
                .marker(StringUtil.isEmpty(nextContinuationToken) ? null : nextContinuationToken)
                .prefix(StringUtil.isEmpty(prefix) ? null : prefix)
                .build();
        ListObjectsResponse listObjectsResponse = null;
        try {
            log.info("listObjectsAndRule ->  endPoint:{},bucket:{},delimiter:{},prefix:{},marker:{}", ossBean.getEndPoint(), request.bucket(), request.delimiter(), request.prefix(), request.marker());
            listObjectsResponse = s3Client.listObjects(request);
        } catch (Exception e) {
            log.error("listObjectsAndRule -> {}", e.getMessage(), e);
            throw new RuntimeException("云存查询失败！");
        }
        List<S3Object> contents = listObjectsResponse.contents();
        String s = listObjectsResponse.nextMarker();
        List<CommonPrefix> commonPrefixes = listObjectsResponse.commonPrefixes();
        List<S3ObjectDto.ObjectDto> list = new ArrayList<>();

        Map<String, Integer> rules = new HashMap<>();
        try {
            GetBucketLifecycleConfigurationRequest lifecycleRequest = GetBucketLifecycleConfigurationRequest.builder()
                    .bucket(ossBean.getBucket())  // 填写存储桶的名称
                    .build();
            GetBucketLifecycleConfigurationResponse lifecycleResponse = s3Client.getBucketLifecycleConfiguration(lifecycleRequest);
            log.info("getBucketLifecycleConfiguration -> {}", lifecycleResponse.toString());
            if (!lifecycleResponse.rules().isEmpty()) {
                lifecycleResponse.rules().forEach(e -> {
                    String prefixTemp = null;
                    if (e.filter() != null) {
                        prefixTemp = e.filter().prefix();
                    }
                    if (StringUtil.isEmpty(prefixTemp)){
                        prefixTemp = e.prefix();
                    }
                    if (StringUtil.isNotEmpty(prefixTemp) && e.expiration() != null && e.expiration().days() != null) {
                        rules.put(prefixTemp, e.expiration().days());
                    }
                });
            }
        } catch (Exception e) {
            log.info("getBucketLifecycleConfiguration err -> {}", e.getMessage(), e);
        }

        commonPrefixes.forEach(e -> {
            S3ObjectDto.ObjectDto dto = new S3ObjectDto.ObjectDto();
            dto.setType(1);
            dto.setKey(e.prefix());
            String[] split = e.prefix().split("/");
            if (split.length > 1) {
                dto.setName(split[split.length - 1]);
            } else if (split.length == 1) {
                dto.setName(split[0]);
            } else {
                dto.setName(e.prefix());
            }
            if (rules.containsKey(e.prefix())) {
                dto.setDays(rules.get(e.prefix()));
            } else {
                dto.setDays(rules.getOrDefault(e.prefix().substring(0, e.prefix().length() - 1), -1));
            }
            list.add(dto);
        });
        contents.forEach(e -> {
            S3ObjectDto.ObjectDto dto = new S3ObjectDto.ObjectDto();
            dto.setType(2);
            dto.setKey(e.key());
            String[] split = e.key().split("/");
            if (split.length > 1) {
                dto.setName(split[split.length - 1]);
            } else {
                dto.setName(e.key());
            }
            dto.setSize(e.size());
            dto.setLastModified(DateUtil.format(Date.from(e.lastModified()), DatePattern.NORM_DATETIME_PATTERN));
            list.add(dto);
        });
        S3ObjectDto res = new S3ObjectDto();
        res.setContents(list);
        res.setNextContinuationToken(s);
        return res;
    }

    @Override
    public void upload(OSSBean ossBean, File file, String ossObjKey) {
        try (InputStream fileStream = Files.newInputStream(file.toPath())) {
            S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
            // 上传 Object
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(ossBean.getBucket())
                    .key(ossObjKey) // 目标S3上的路径和文件名
                    .contentType(FileUtil.getMimeType(file.getName()))
                    .build();
            s3Client.putObject(request, RequestBody.fromInputStream(fileStream,
                    file.length()));
        } catch (Exception e) {
            log.error("upload -> {}", e.getMessage(), e);
            throw new BizRuntimeException("上传失败");
        }
    }

    @Override
    public void upload(OSSBean ossBean, InputStream inputStream, Long size, String ossObjKey, String fileName) {
        S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
        if (StringUtil.isEmpty(fileName)) {
            fileName = ossObjKey;
        }
        String contentType = ContentTypeUtil.getMimeType(fileName);
        // 上传 Object
        PutObjectRequest request = PutObjectRequest.builder()
                .contentType(contentType)
                .bucket(ossBean.getBucket())
                .acl(ObjectCannedACL.PUBLIC_READ)
                .key(ossObjKey) // 目标S3上的路径和文件名
                .build();
        log.info("s3上传开始：ossBean:{},ossObjKey:{},size:{}", ossBean, ossObjKey, size);
        PutObjectResponse putObjectResponse = s3Client.putObject(request, RequestBody.fromInputStream(inputStream, size));
        log.info("s3上传结束：ossBean:{},ossObjKey:{},size:{},putObjectResponse:{}", ossBean, ossObjKey, size, JSON.toJSONString(putObjectResponse));
    }

    public static void main(String[] args) {
        OSSBean build = OSSBean
                .builder()
                .accessKey("ccc696a3c25f16ec8310")
                .secretKey("b80d00dce53734b0ea1abc1476eb1593436fe224")
                .endPoint("https://oos-xiongan.ctyunapi.cn")
                .returnPoint("https://oos-xiongan.ctyunapi.cn")
                .bucket("3days")
                .region("xiongan")
                .build();
        DefS3Impl defS3 = new DefS3Impl();
        String s = defS3.downloadUrl(build, "sdkAlarm/null/guanyu-003/385/524f19ad.jpg");
        System.out.println(s);
    }

    @Override
    public String downloadUrl(OSSBean ossBean, String ossObjKey) {
        try {
            AwsBasicCredentials credentials = AwsBasicCredentials.create(ossBean.getAccessKey(), ossBean.getSecretKey());
            try (
                    // 构建 S3 客户端
                    S3Presigner presigner = S3Presigner.builder()
                            // 这里同样得需要对应得后端对象网关得地址，http或者https都可以
                            .endpointOverride(new URI(ossBean.getEndPoint()))
                            .credentialsProvider(StaticCredentialsProvider.create(credentials))
                            .region(Region.of(ossBean.getRegion())) // this is not used, but the AWS SDK requires it
                            .serviceConfiguration(S3Configuration.builder().pathStyleAccessEnabled(true).build())  // 强制路径样式
                            .build()) {
                PresignedGetObjectRequest presignedRequest =
                        presigner.presignGetObject(preReq -> {
                            preReq.getObjectRequest(req -> {
                                req.bucket(ossBean.getBucket()).key(ossObjKey);
                            }).signatureDuration(
                                    Duration.ofHours(24)
                            );
                        });
                return presignedRequest.url().toString();
            }
        } catch (Exception e) {
            log.error("downloadUrl -> {}", e.getMessage(), e);
            throw new BizRuntimeException("获取下载链接失败");
        }
    }

    private final Map<String, S3Client> s3ClientMap = new ConcurrentHashMap<>();

    public S3Client getS3Client(String accessKey, String secretKey, String endPoint, String region) {
        return s3ClientMap.computeIfAbsent(accessKey + secretKey + endPoint + region, (k) -> {
            try {
                return S3Client.builder()
                        .endpointOverride(new URI(endPoint))
                        .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)))
                        .serviceConfiguration(S3Configuration.builder().pathStyleAccessEnabled(true).build())
                        .region(Region.of(region))
                        .build();
            } catch (URISyntaxException e) {
                log.error("getS3Client -> {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public boolean doesBucketExist(OSSBean ossBean) {
        try {
            S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
            software.amazon.awssdk.services.s3.model.ListObjectsRequest listObjectsRequest = ListObjectsRequest.builder()
                    .bucket(ossBean.getBucket()).build();
            ListObjectsResponse listObjectsResponse = s3Client.listObjects(listObjectsRequest);
            return true;
        } catch (SdkClientException e) {
            log.error("doesBucketExist {}", e.getMessage(), e);
            return false;
        }
    }


    @Override
    public void setBucketLifecycleConfiguration(OSSBean ossBean, List<S3Rule> s3Rules) {
        S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
        Map<String, LifecycleRule> rules = new HashMap<>();
        try {
            GetBucketLifecycleConfigurationRequest lifecycleRequest = GetBucketLifecycleConfigurationRequest.builder()
                    .bucket(ossBean.getBucket())  // 填写存储桶的名称
                    .build();
            GetBucketLifecycleConfigurationResponse lifecycleResponse = s3Client.getBucketLifecycleConfiguration(lifecycleRequest);
            if (!lifecycleResponse.rules().isEmpty()) {
                lifecycleResponse.rules().forEach(rule -> {
                    String prefixTemp = null;
                    if (rule.filter() != null) {
                        prefixTemp = rule.filter().prefix();
                    }
                    if (StringUtil.isEmpty(prefixTemp)){
                        prefixTemp = rule.prefix();
                    }
                    rules.put(prefixTemp, rule);
                });
            }
            log.info("获取桶的生命周期 defs3 ossBean:{} getBucketLifecycleConfiguration -> {}", ossBean, JSON.toJSON(rules));
        } catch (Exception e) {
            log.info("获取桶的生命周期 err ossBean:{} getBucketLifecycleConfiguration -> {}", ossBean, VlinkerThrowableUtil.getMsg(e));
        }
        for (S3Rule s3Rule : s3Rules) {
            LifecycleRule rule = LifecycleRule.builder()
                    .id(s3Rule.getId())
                    .filter(LifecycleRuleFilter.builder().prefix(s3Rule.getPrefix()).build())
                    .status(ExpirationStatus.ENABLED)
                    .expiration(LifecycleExpiration.builder()
                            .days(s3Rule.getExpirationInDays())
                            .build())
                    .build();
            rules.put(s3Rule.getPrefix(), rule);
        }

        PutBucketLifecycleConfigurationRequest request = PutBucketLifecycleConfigurationRequest.builder()
                .bucket(ossBean.getBucket())
                .lifecycleConfiguration(BucketLifecycleConfiguration.builder()
                        .rules(rules.values())
                        .build())
                .build();
        try {
            log.info("设置生命周期 req:{} request:{}", s3Rules, JSON.toJSONString(request));
            PutBucketLifecycleConfigurationResponse putBucketLifecycleConfigurationResponse = s3Client.putBucketLifecycleConfiguration(request);
            SdkHttpResponse sdkHttpResponse = putBucketLifecycleConfigurationResponse.sdkHttpResponse();
            log.info("设置生命周期 req:{} statusCode: {} statusText:{}", s3Rules, sdkHttpResponse.statusCode(), sdkHttpResponse.statusText().orElse("???"));
        } catch (Exception e) {
            log.error("设置生命周期失敗 req:{} putBucketLifecycleConfigurationResponse: {}", s3Rules, e.getMessage());
        }
        try {
            PutBucketCorsRequest bucketCorsRequest = PutBucketCorsRequest
                    .builder()
                    .corsConfiguration(
                            CORSConfiguration.builder()
                                    .corsRules(
                                            CORSRule.builder()
                                                    .allowedOrigins(Collections.singletonList("*"))
                                                    .allowedMethods(Arrays.asList("GET", "PUT", "POST", "DELETE", "HEAD"))
                                                    .allowedHeaders(Collections.singletonList("*"))
                                                    .maxAgeSeconds(3000)
                                                    .build())
                                    .build())
                    .bucket(ossBean.getBucket())
                    .build();
            PutBucketCorsResponse putBucketCorsResponse = s3Client.putBucketCors(bucketCorsRequest);
            log.info("设置桶的Cors req:{} putBucketCorsResponse: {}", s3Rules, putBucketCorsResponse);
        } catch (Exception e) {
            log.error("设置桶的Cors req:{} putBucketCorsResponse -> {}", s3Rules, VlinkerThrowableUtil.getMsg(e));
        }
    }

    @Override
    public List<S3Rule> getBucketLifecycleConfiguration(OSSBean ossBean) {
        S3Client s3Client = getS3Client(ossBean.getAccessKey(), ossBean.getSecretKey(), ossBean.getEndPoint(), ossBean.getRegion());
        GetBucketLifecycleConfigurationRequest request = GetBucketLifecycleConfigurationRequest.builder().bucket(ossBean.getBucket()).build();
        GetBucketLifecycleConfigurationResponse bucketLifecycleConfiguration = s3Client.getBucketLifecycleConfiguration(request);
        List<LifecycleRule> rules = bucketLifecycleConfiguration.rules();
        List<S3Rule> res = new ArrayList<>();
        rules.forEach(rule -> {
            S3Rule s3Rule = new S3Rule();
            s3Rule.setId(rule.id());
            if (rule.filter() != null) {
                s3Rule.setPrefix(rule.filter().prefix());
            } else {
                s3Rule.setPrefix(rule.prefix());
            }
            s3Rule.setStatus(rule.statusAsString());
            if (rule.expiration().days() != null) {
                s3Rule.setExpirationInDays(rule.expiration().days());
            }
            res.add(s3Rule);
        });
        return res;
    }

    @Override
    public Double getBucketNowSize(OSSBean ossBean) {
        return -1D;
    }


}
