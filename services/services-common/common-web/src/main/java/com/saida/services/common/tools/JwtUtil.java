package com.saida.services.common.tools;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.saida.services.constant.AuthConstants;
import com.saida.services.system.sys.pojo.JwtUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class JwtUtil {

    private static final ThreadLocal<JwtUser> currentUser = new ThreadLocal<>();

    public static void setUser(JwtUser user) {
        currentUser.set(user);
    }

    private static JwtUser getJwtUser() {
        JwtUser jwtUser = currentUser.get();
        if (jwtUser == null) {
            // 理论上不会走到这里，因为 Filter 已经初始化
            jwtUser = parseJwtUserFromRequest();
            currentUser.set(jwtUser);
        }
        return jwtUser;
    }

    public static void clear() {
        currentUser.remove();
    }

    /**
     * 从请求头中解析JwtUser
     */
    public static JwtUser parseJwtUserFromRequest() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (null == requestAttributes) {
                return new JwtUser();
            }
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            String payload = request.getHeader(AuthConstants.JWT_PAYLOAD_KEY);
            if (StringUtil.isEmpty(payload)) {
                return new JwtUser();
            }
            payload = URLDecoder.decode(payload, StandardCharsets.UTF_8);
            JwtUser javaObject = JSON.toJavaObject(JSONObject.parseObject(payload), JwtUser.class);
            if (javaObject == null) {
                log.error("JWT解析失败 javaObject is null ...msg=【{}】", payload);
                return new JwtUser();
            }
            return javaObject;
        } catch (Exception e) {
            log.error("JWT解析失败...msg=【{}】", e.getMessage(), e);
            return new JwtUser();
        }
    }

    public static Long getUserId() {
        return getJwtUser().getId();
    }

    public static Long getOrgId() {
        return getJwtUser().getOrgId();
    }

    public static Set<Long> getOrgIdSet() {
        return getJwtUser().getOrgIdSet();
    }

    public static String getAccount() {
        return getJwtUser().getAccount();
    }

    public static String getUserName() {
        return getJwtUser().getName();
    }

    public static String getToCustomerName() {
        return getJwtUser().getToCustomerName();
    }

    public static String getPhone() {
        return getJwtUser().getPhone();
    }

    public static Integer getType() {
        return getJwtUser().getType();
    }

    public static Long getAppId() {
        return getJwtUser().getAppId();
    }

    /**
     * 是否为内部应用
     */
    public static boolean isInternal() {
        Integer internal = getJwtUser().getInternal();
        return internal != null && internal == 1;
    }

    public static String getIotAppId() {
        try {
            return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest().getHeader(AuthConstants.APP_ID);
        } catch (Exception e) {
            log.error("JWT获取 appId 失败...msg=【{}】", e.getMessage());
            return null;
        }
    }

    public static String getAppKey() {
        return getJwtUser().getAppKey();
    }

    public static JwtUser getUserInfo() {
        return getJwtUser();
    }

    /**
     * 是否为超管账号
     */
    public static boolean isSupper() {
        return 2 == getJwtUser().getIsSuper();
    }

    public static String getJti() {
        try {
            String authorization = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest().getHeader(AuthConstants.AUTHORIZATION_KEY);
            if (CharSequenceUtil.isEmpty(authorization)) {
                return null;
            }
            JWSObject jwsObject = JWSObject.parse(authorization);
            Map<String, Object> map = jwsObject.getPayload().toJSONObject();
            Object obj = map.get(AuthConstants.JWT_JTI);
            return obj == null ? null : obj.toString();
        } catch (ParseException e) {
            log.error("JWT获取 Jti 失败...msg=【{}】", e.getMessage());
            return null;
        }
    }


    private static KeyPair keyPair = null;
    private static RSASSAVerifier rsassaVerifier = null;


    public static JWSObject checkRefreshToken(String refreshToken) {
        if (CharSequenceUtil.isEmpty(refreshToken)) {
            return null;
        }
        if (keyPair == null) {
            keyPair = SpringUtil.getBean(KeyPair.class);
        }
        if (rsassaVerifier == null) {
            try {
                X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyPair.getPublic().getEncoded());
                KeyFactory keyFactory = KeyFactory.getInstance("RSA");
                RSAPublicKey rsaPublicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);
                rsassaVerifier = new RSASSAVerifier(rsaPublicKey);
            } catch (Exception e) {
                log.error("获取公钥失败", e);
                return null;
            }
        }

        JWSObject jwsObject = null;
        try {
            jwsObject = JWSObject.parse(refreshToken);
        } catch (ParseException e) {
            log.error("refreshToken解析失败", e);
            return null;
        }
        if (keyPair != null) {
            try {
                if (!jwsObject.verify(rsassaVerifier)) {
                    return null;
                }
            } catch (JOSEException e) {
                log.error("refreshToken校验失败", e);
            }
        }
        return jwsObject;
    }


    public static Long getEnterpriseOrgId() {
        return getJwtUser().getEnterpriseOrgId();
    }

    /**
     * 获取当前登录用户的企业ID
     * 运营获取的是当前机构id
     * 企业获取的是企业id
     */
    public static Long getNowSubOrgId() {
        JwtUser jwtUser = getJwtUser();
        return jwtUser.getEnterpriseOrgId() == null ? jwtUser.getOrgId() : jwtUser.getEnterpriseOrgId();
    }
}