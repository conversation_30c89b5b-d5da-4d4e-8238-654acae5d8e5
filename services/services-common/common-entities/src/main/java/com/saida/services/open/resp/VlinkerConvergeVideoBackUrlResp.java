package com.saida.services.open.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 视频汇聚平台-查询录像播放地址响应实体类
 */
@Getter
@Setter
public class VlinkerConvergeVideoBackUrlResp implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer accessWay;
    // 1 标准 2 赛达私有流
    private Integer playFun;

    /**
     * 播放地址
     */
    private String url;
    /**
     * 下载地址
     */
    private String downloadUrl;
}