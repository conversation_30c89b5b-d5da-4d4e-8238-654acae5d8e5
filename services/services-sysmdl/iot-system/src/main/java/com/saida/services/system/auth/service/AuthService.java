package com.saida.services.system.auth.service;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.HomePageUrlConfig;
import com.saida.services.common.config.JwtConfig;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.enumeration.StatusEnum;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.auth.jwt.JwtGenerator;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.sys.entity.*;
import com.saida.services.system.sys.pojo.JwtUser;
import com.saida.services.system.sys.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class AuthService {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtGenerator jwtGenerator;

    @Autowired
    private RedisUtil redisUtil;


    /**
     * @return Token
     */
    public DtoResult<Token> getTokenByAdmin() {
        UserEntity user = userService.getOne(new LambdaQueryWrapper<UserEntity>()
                .eq(UserEntity::getIsSuper, 2)
                .last(" limit 1 "));
        if (user == null) {
            return DtoResult.error("系统管理员不存在，请先添加管理员");
        }
        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            return DtoResult.error("账号已被禁用");
        }
        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return DtoResult.ok(createAccessToken(user));
    }


    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private RolePermissionService rolePermissionService;
    @Autowired
    private PermissionService permissionService;
    @Resource
    private HomePageUrlConfig homePageUrlConfig;

    public DtoResult<JumpDto> loginToAdminByUser(LoginToAdminByUser loginToAdminByUser) {

        String jumpUrl = homePageUrlConfig.getJumpUrl(loginToAdminByUser.getPid());
        if (jumpUrl == null) {
            return DtoResult.error("服务未启用");
        }
        log.info("loginToAdminByUser:{}", loginToAdminByUser);
        if (loginToAdminByUser.getUserId() == null || loginToAdminByUser.getUserId() <= 0
                || loginToAdminByUser.getUserName() == null || loginToAdminByUser.getAccount() == null
                || loginToAdminByUser.getPassword() == null) {
            return DtoResult.error("参数错误","你怎么能少给我东西呢");
        }
        Long adminRoleId = 100000L + loginToAdminByUser.getPid();
        RoleEntity adminRole = roleService.getById(adminRoleId);
        if (adminRole == null) {
            // 创建一个默认的
            adminRole = new RoleEntity();
            adminRole.setId(adminRoleId);
            adminRole.setName("系统默认管理员");
            adminRole.setStatus(1);
            adminRole.setRemark("系统默认管理员");
            adminRole.setCreateUser(loginToAdminByUser.getUserId());
            adminRole.setCreateTime(new Date());
            adminRole.setPid(loginToAdminByUser.getPid());
            roleService.save(adminRole);
            List<PermissionEntity> list = permissionService.list();
            List<RolePermissionEntity> rolePermissionEntityList = new ArrayList<>();
            RoleEntity finalAdminRole = adminRole;
            list.forEach(e -> {
                RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
                rolePermissionEntity.setRid(finalAdminRole.getId());
                rolePermissionEntity.setPid(e.getId());
                rolePermissionEntity.setCreateUser(loginToAdminByUser.getUserId());
                rolePermissionEntity.setCreateTime(new Date());
                rolePermissionEntityList.add(rolePermissionEntity);
            });
            rolePermissionService.saveBatch(rolePermissionEntityList);
        }

        UserEntity user = userService.getOne(new LambdaQueryWrapper<UserEntity>()
                .eq(UserEntity::getYyId, loginToAdminByUser.getUserId())
                .eq(UserEntity::getPid, loginToAdminByUser.getPid()));
        if (user == null) {
            user = new UserEntity();
            String account = loginToAdminByUser.getAccount();
            // 这个 account 可能会重复
            int i = 1;
            while (true) {
                if (i > 100) {
                    return DtoResult.error("登录失败", "登录失败，我实在没办法给你分配一个可以使用的账号");
                }
                if (userService.count(new LambdaQueryWrapper<UserEntity>()
                        .eq(UserEntity::getAccount, account + i)
                        .eq(UserEntity::getPid, loginToAdminByUser.getPid())) == 0) {
                    break;
                }
                i++;
            }
            user.setAccount(account + i);
            user.setName(loginToAdminByUser.getUserName());
            user.setPassword(loginToAdminByUser.getPassword());
            user.setPid(loginToAdminByUser.getPid());
            user.setYyId(loginToAdminByUser.getUserId());
            userService.save(user);
        } else {
            if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
                return DtoResult.error("账号已被禁用");
            }
        }

        UserRoleEntity userRoleEntity = userRoleService.getOne(new LambdaQueryWrapper<UserRoleEntity>()
                .eq(UserRoleEntity::getUid, user.getId())
                .eq(UserRoleEntity::getRid, adminRole.getId()));
        if (userRoleEntity == null) {
            userRoleEntity = new UserRoleEntity();
            userRoleEntity.setUid(user.getId());
            userRoleEntity.setRid(adminRole.getId());
            userRoleEntity.setCreateUser(loginToAdminByUser.getUserId());
            userRoleEntity.setCreateTime(new Date());
            userRoleService.save(userRoleEntity);
        }
//        return DtoResult.ok(createAccessToken(user));

        // 为什么要这么做呢 因为有些参数是db的默认值 如果不回查的话 user有些字段是空的 会导致jwt生成的数据也是错误的
        UserEntity userEntity = userService.getById(user.getId());
        if (userEntity == null) {
            log.error("用户不存在:{}", user.getId());
            return DtoResult.error("用户不存在", "很离奇的问题");
        }
        userEntity.setPassword(null);
        userEntity.setPageNum(null);
        userEntity.setPageSize(null);
        Token accessToken = createAccessToken(userEntity);
        JumpDto build = JumpDto.builder()
                .type(loginToAdminByUser.getPid())
                .url(jumpUrl)
                .token(accessToken)
                .jumpUrl(jumpUrl + "?token=" + accessToken.getAccessToken() + "&unsecret=1")
                .build();
        log.info("loginToAdminByUser JumpDto:{}", build);
        return DtoResult.ok(build);

    }

    /**
     * 根据用户名密码获取token
     * @param username 用户名
     * @param password 加密后的密码
     * @return Token
     */
    public Token getTokenByUserNamePassword(String username, String password) {
        if (StringUtil.isEmpty(username) || StringUtil.isEmpty(password)) {
            throw new BizRuntimeException("账号密码必填");
        }
        UserEntity user = checkLoginByUserNamePassword(username, password);

        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            throw new BizRuntimeException("账号已被禁用");
        }

        user = userService.getInfo(user.getId());

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        Object recordLog = request.getAttribute("log_record");
        OperatorLogEntity logEntity = new OperatorLogEntity();
        if (recordLog != null) {
            logEntity = (OperatorLogEntity) recordLog;
            logEntity.setAccount(user.getAccount());
            logEntity.setUserName(user.getName());
            logEntity.setUserId(user.getId());
            request.setAttribute("log_record", logEntity);
        }

        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return createAccessToken(user);
    }


    /**
     * 根据手机号获取token
     * @param phone 手机号
     * @return Token
     */
    public Token getTokenByPhone(String phone) {
        if (StringUtil.isEmpty(phone)) {
            throw new BizRuntimeException("手机号必填");
        }
        UserEntity user = userService.getByAccountOrPhone(phone);
        if (user == null) {
            throw new BizRuntimeException("账号不存在");
        }

        user = userService.getInfo(user.getId());
        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            throw new BizRuntimeException("账号已被禁用");
        }

        user = userService.getInfo(user.getId());

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        Object recordLog = request.getAttribute("log_record");
        OperatorLogEntity logEntity = new OperatorLogEntity();
        if (recordLog != null) {
            logEntity = (OperatorLogEntity) recordLog;
            logEntity.setAccount(user.getAccount());
            logEntity.setUserName(user.getName());
            logEntity.setUserId(user.getId());
            request.setAttribute("log_record", logEntity);
        }

        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return createAccessToken(user);
    }

    /**
     * 根据用户名密码获取用户信息
     * @param username 用户名
     * @param password 加密后的密码
     * @return UserEntity
     */
    public UserEntity checkLoginByUserNamePassword(String username, String password) {
        UserEntity user = userService.getByAccountOrPhone(username);
        if (user == null) {
            throw new BizRuntimeException("账号或密码错误");
        }
        String key = "IOT:LOGIN:FAIL:" + username;
        Object loginFail = redisUtil.get(key);
        int loginFailCount = 0;
        if (loginFail != null) {
            loginFailCount = Integer.parseInt(loginFail.toString());
        }
        if (loginFailCount >= 5) {
            throw new BizRuntimeException("账户已被锁定，请15分钟后再试！");
        }
        if (BCrypt.checkpw(password, user.getPassword())) {
            redisUtil.del(key);
            return user;
        }
        loginFailCount = loginFailCount + 1;
        redisUtil.set(key, String.valueOf(loginFailCount), 60 * 15);
        if (loginFailCount >= 5) {
            throw new BizRuntimeException("账户已被锁定，请15分钟后再试！");
        }
        throw new BizRuntimeException("账号或密码错误，" + (5 - loginFailCount) + "次错误后账号锁定15分钟");
    }

    /**
     * 根据用户信息生成token
     * @param user 用户信息
     * @return Token
     */
    public Token createAccessToken(UserEntity user) {
        if (user == null) {
            throw new BizRuntimeException("用户信息错误");
        }
        JwtUser jwtUser = new JwtUser();
        BeanUtils.copyProperties(user, jwtUser);
        String accessToken = jwtGenerator.createAccessToken(jwtUser, user);
        String refreshToken = jwtGenerator.createRefreshToken(jwtUser);
        return Token.builder().accessToken(accessToken).accessTokenExpire(JwtConfig.getInstance().getTokenExpire() * 60L).refreshToken(refreshToken).build();
    }

    public void logout() {
        String jti = JwtUtil.getJti();
        if (StringUtil.isEmpty(jti)) {
            throw new BizRuntimeException("认证信息错误");
        }
        redisUtil.del(RedisConstants.USER_JWT + jti);
    }
}
