package com.saida.services.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.saida.services.annotction.ThirdPartyPlatformsBizAnno;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.ThirdPartyPlatformsBizComponent;
import com.saida.services.converge.deviceApi.req.*;
import com.saida.services.converge.entity.dto.DeviceAndChannelDto;
import com.saida.services.converge.entity.dto.DeviceChannelDto;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.qxNode.resp.sd.ConvergeSdCardCapacityResp;
import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
import com.saida.services.converge.vo.GbControlLocalPlaybackVo;
import com.saida.services.converge.vo.GbStopDownloadLocalPlaybackVo;
import com.saida.services.deviceApi.req.CommonGetHumanoidMarkersReq;
import com.saida.services.deviceApi.req.CommonGetSoundAndLightShockReq;
import com.saida.services.deviceApi.resp.*;
import com.saida.services.enums.BaseVideoProtocolEnum;
import com.saida.services.feign.converge.system.IFeignConvergeSystemApiController;
import com.saida.services.open.biz.req.*;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseDeletePrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseJumpPrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBasePrePointListReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseSetPrePointReq;
import com.saida.services.open.biz.req.rtc.ThirdPartyPlatformsBaseRtcConnectReq;
import com.saida.services.open.biz.resp.*;
import com.saida.services.open.deviceApi.req.*;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.open.req.*;
import com.saida.services.open.req.rtc.VlinkerRtcConnectReq;
import com.saida.services.open.resp.*;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.biz.service.ThirdPartyVideoPlatformsService;
import com.saida.services.system.sys.dto.SysOrgDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ThirdPartyPlatformsBizAnno(
        componentValue = ThirdPartyPlatformsBizComponent.VideoBizComponent.vlinkerConvergeBizComponentName,
        componentName = "V-LINKER视频汇聚平台",
        componentDes = "",
        platformType = OpenThirdPartyPlatformsTypeEnum.VIDEO
)
@Component(ThirdPartyPlatformsBizComponent.VideoBizComponent.vlinkerConvergeBizComponentName)
public class VlinkerConvergeServiceImpl implements ThirdPartyVideoPlatformsService {

    @Autowired
    private IFeignConvergeSystemApiController feignConvergeSystemApiController;

    @Override
    public DtoResult<Void> refreshToken(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        return DtoResult.ok();
    }


    @Override
    public DtoResult<List<Tree<String>>> getDeviceTreeList(ThirdPartyPlatformsBaseDeviceTreeReq req) {
        DtoResult<List<SysOrgDto>> dtoResult = feignConvergeSystemApiController.getDeviceTreeList();
        if (!dtoResult.success()) {
            return DtoResult.error(dtoResult.getMessage());
        }
        List<SysOrgDto> deviceTreeList = dtoResult.getData();
        // 转换器
        List<Tree<String>> treeNodeList = TreeUtil.build(deviceTreeList, "0", this.getTreeNodeConfig(),
                (treeNode, tree) -> {
                    tree.setId(String.valueOf(treeNode.getId()));
                    tree.setParentId(String.valueOf(treeNode.getParentId()));
                    tree.setName(treeNode.getName());
                });
        return DtoResult.ok(treeNodeList);
    }

    @Override
    public DtoResult<List<ThirdPartyPlatformsChannelListResp>> getChannelList(String sn) {
        VlinkerConvergeChannelListReq req = new VlinkerConvergeChannelListReq();
        req.setSn(sn);
        DtoResult<List<DeviceChannelDto>> channelList = feignConvergeSystemApiController.getChannelList(req);
        if (channelList.success()) {
            List<ThirdPartyPlatformsChannelListResp> respList = channelList.getData().stream().map(channel -> {
                ThirdPartyPlatformsChannelListResp resp = new ThirdPartyPlatformsChannelListResp();
                resp.setChannelId(channel.getChannelId());
                resp.setChannelName(channel.getChannelName());
                return resp;
            }).collect(Collectors.toList());
            return DtoResult.ok(respList);
        } else {
            return DtoResult.error(channelList.getMessage());
        }
    }

    @Override
    public DtoResult<BasePageInfoEntity<ThirdPartyPlatformsDeviceListResp>> getDeviceList(ThirdPartyPlatformsBaseDeviceListReq req) {
        try {
            VlinkerConvergeDeviceListReq vlinkerConvergeDeviceListReq = new VlinkerConvergeDeviceListReq();
            vlinkerConvergeDeviceListReq.setPageNum(req.getPageNum());
            vlinkerConvergeDeviceListReq.setPageSize(null != req.getViewPage() && req.getViewPage() ? req.getPageSize() : 1000);
            vlinkerConvergeDeviceListReq.setOrgId(req.getDeviceTreeId());
            vlinkerConvergeDeviceListReq.setSub(req.getSubRegion());
            vlinkerConvergeDeviceListReq.setDeviceName(req.getDeviceName());
            vlinkerConvergeDeviceListReq.setSn(req.getDeviceCode());
            vlinkerConvergeDeviceListReq.setSn(req.getSnCode());

            DtoResult<BasePageInfoEntity<DeviceAndChannelDto>> dtoResult = feignConvergeSystemApiController.getDeviceChannelList(vlinkerConvergeDeviceListReq);
            if (!dtoResult.success()) {
                return com.saida.services.common.base.DtoResult.error(dtoResult.getMessage());
            }
            log.info("获取设备列表成功...{}", dtoResult);
            BasePageInfoEntity<DeviceAndChannelDto> deviceList = dtoResult.getData();

            BasePageInfoEntity<ThirdPartyPlatformsDeviceListResp> basePageInfoEntity = new BasePageInfoEntity<>();
            basePageInfoEntity.setTotal(deviceList.getTotal());
            basePageInfoEntity.setCurrent(deviceList.getCurrent());
            basePageInfoEntity.setSize(deviceList.getSize());
            basePageInfoEntity.setPages(deviceList.getPages());
            if (CollectionUtil.isEmpty(deviceList.getRecords())) {
                return com.saida.services.common.base.DtoResult.ok(basePageInfoEntity);
            }
            basePageInfoEntity.setRecords(deviceList.getRecords().stream().map(t1 -> {
                ThirdPartyPlatformsDeviceListResp thirdPartyPlatformsDeviceListResp = new ThirdPartyPlatformsDeviceListResp();
                if (StringUtil.isNotEmpty(t1.getId())) {
                    thirdPartyPlatformsDeviceListResp.setId(String.valueOf(t1.getId()));
                }
                if (StringUtil.isNotEmpty(t1.getDeviceCode())) {
                    thirdPartyPlatformsDeviceListResp.setPlatformUniqueCode(t1.getDeviceCode());
                }
                if (StringUtil.isNotEmpty(t1.getName())) {
                    thirdPartyPlatformsDeviceListResp.setName(t1.getName());
                }
                if (StringUtil.isNotEmpty(t1.getDeviceCode())) {
                    thirdPartyPlatformsDeviceListResp.setSnCode(t1.getDeviceCode());
                    thirdPartyPlatformsDeviceListResp.setDeviceCode(t1.getDeviceCode());
                }
                if (StringUtil.isNotEmpty(t1.getModel())) {
                    thirdPartyPlatformsDeviceListResp.setDeviceModel(t1.getModel());
                }
                thirdPartyPlatformsDeviceListResp.setOrgId(t1.getOrgId());
                if (StringUtil.isNotEmpty(t1.getChannelStatus())) {
                    thirdPartyPlatformsDeviceListResp.setIsOnline(String.valueOf(t1.getChannelStatus()));
                }
                if (StringUtil.isNotEmpty(t1.getPassword())) {
                    thirdPartyPlatformsDeviceListResp.setDeviceVerificationCode(t1.getPassword());
                }
                thirdPartyPlatformsDeviceListResp.setGateType(t1.getGateType());
                thirdPartyPlatformsDeviceListResp.setAccessWay(t1.getAccessWay());
                thirdPartyPlatformsDeviceListResp.setChannelId(t1.getChannelId());
                thirdPartyPlatformsDeviceListResp.setChannelName(t1.getChannelName());

                if (t1.getDeviceModelVersionEntity() != null) {
                    thirdPartyPlatformsDeviceListResp.setDeviceCapacity(t1.getDeviceModelVersionEntity().getDeviceCapacity());
                }
                if (t1.getDeviceModelEntity() != null) {
                    thirdPartyPlatformsDeviceListResp.setGateType(t1.getDeviceModelEntity().getTurnstile() - 1);
                    thirdPartyPlatformsDeviceListResp.setIpcType(t1.getDeviceModelEntity().getDeviceType().intValue());
                }
                return thirdPartyPlatformsDeviceListResp;
            }).collect(Collectors.toList()));
            return DtoResult.ok(basePageInfoEntity);
        } catch (Exception e) {
            log.error("根据设备树获取设备列表出错...msg={}", e.getMessage(), e);
            return DtoResult.error("根据设备树获取设备列表出错");
        }
    }

    /**
     * 获取第三方平台视频直播URL
     *
     * @param baseVideoLiveUrlReq 直播URL请求基础信息
     * @return 返回直播URL响应结果
     */
    @Override
    public DtoResult<ThirdPartyPlatformsVideoLiveUrlResp> getVideoLiveUrl(ThirdPartyPlatformsBaseVideoLiveUrlReq baseVideoLiveUrlReq) {
        // 校验协议是否为空
        if (baseVideoLiveUrlReq.getProtocol().length == 0) {
            return DtoResult.error("协议错误！");
        }
        // 构造请求参数
        VlinkerConvergeVideoLiveUrlReq req = new VlinkerConvergeVideoLiveUrlReq();
        req.setDeviceCode(baseVideoLiveUrlReq.getDeviceCode());
        req.setChannelId(baseVideoLiveUrlReq.getChannelId());
        req.setActiveSecond(baseVideoLiveUrlReq.getActiveSecond());
        req.setStreamIndex(baseVideoLiveUrlReq.getStreamIndex());
        req.setNetworking(baseVideoLiveUrlReq.getNetworking());
        req.setMute(baseVideoLiveUrlReq.getMute());
        req.setOpenAi(baseVideoLiveUrlReq.getOpenAi());
        req.setStartAi(baseVideoLiveUrlReq.getStartAi());
        BaseVideoProtocolEnum[] baseVideoProtocolEnum = baseVideoLiveUrlReq.getProtocol();
        // 遍历协议数组，根据协议类型设置直播URL
        for (BaseVideoProtocolEnum videoProtocolEnum : baseVideoProtocolEnum) {
            req.setVideoProtocol(Integer.valueOf(videoProtocolEnum.getCode()));
            // 调用Feign接口获取直播URL
            DtoResult<StreamUrlDto> urlDtoDtoResult = feignConvergeSystemApiController.getVideoLiveUrl(req);
            if (urlDtoDtoResult.success()) {
                StreamUrlDto streamUrlDto = urlDtoDtoResult.getData();
                ThirdPartyPlatformsVideoLiveUrlResp liveUrlResp = new ThirdPartyPlatformsVideoLiveUrlResp();
                liveUrlResp.setSdPushUrl(streamUrlDto.getSdPushUrl());
                if (videoProtocolEnum == (BaseVideoProtocolEnum.SD_URL) && StringUtil.isNotEmpty(streamUrlDto.getSdUrl())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.SD_URL.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getSdUrl());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.HLS) && StringUtil.isNotEmpty(streamUrlDto.getHls())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.HLS.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getHls());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.RTMP) && StringUtil.isNotEmpty(streamUrlDto.getRtmp())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.RTMP.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getRtmp());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.RTSP) && StringUtil.isNotEmpty(streamUrlDto.getRtsp())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.RTSP.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getRtsp());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.WEBRTC) && StringUtil.isNotEmpty(streamUrlDto.getWebrtc())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.WEBRTC.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getWebrtc());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.HTTPSFLV) && StringUtil.isNotEmpty(streamUrlDto.getHttpFlv())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.HTTPSFLV.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getHttpFlv());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.WS_FLV) && StringUtil.isNotEmpty(streamUrlDto.getWsFlv())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.WS_FLV.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getWsFlv());
                    return DtoResult.ok(liveUrlResp);
                }
                if (videoProtocolEnum == (BaseVideoProtocolEnum.SD_WEBRTC) && StringUtil.isNotEmpty(streamUrlDto.getWsFlv())) {
                    liveUrlResp.setProtocolType(BaseVideoProtocolEnum.SD_WEBRTC.getResProtocol());
                    liveUrlResp.setUrl(streamUrlDto.getSdWebRtcUrl());
                    return DtoResult.ok(liveUrlResp);
                }
            } else {
                return DtoResult.error(urlDtoDtoResult.getMessage(), urlDtoDtoResult.getError());
            }
        }
        // 若没有支持的协议，返回错误信息
        return DtoResult.error("取流失败,没有可支持的协议！");
    }

    @Override
    public DtoResult<Void> stopLive(ThirdPartyPlatformsBaseVideoLiveUrlReq baseVideoLiveUrlReq) {
        // 构造请求参数
        VlinkerConvergeVideoLiveUrlReq req = new VlinkerConvergeVideoLiveUrlReq();
        req.setDeviceCode(baseVideoLiveUrlReq.getDeviceCode());
        req.setChannelId(baseVideoLiveUrlReq.getChannelId());
        // 调用Feign接口获取直播URL
        return feignConvergeSystemApiController.stopLive(req);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVirtuallyDataResp> getVirtuallyData(ThirdPartyPlatformsDeviceInfoDto deviceInfoDto) {
        VlinkerConvergeVirtuallyDataReq req = new VlinkerConvergeVirtuallyDataReq();
        req.setDeviceSN(deviceInfoDto.getDeviceCode());
        req.setChannelId(deviceInfoDto.getChannelId());
        // 调用feignSystemApiController.ptzControl方法，发送云台控制请求
        DtoResult<VlinkerConvergeVirtuallyDataResp> dtoResult = feignConvergeSystemApiController.getVirtuallyData(req);
        // 如果请求成功，则返回一个空的DtoResult表示操作成功
        if (dtoResult.success()) {
            ThirdPartyPlatformsVirtuallyDataResp resp = new ThirdPartyPlatformsVirtuallyDataResp();
            BeanUtil.copyProperties(dtoResult.getData(), resp);
            return DtoResult.ok(resp);
        }
        // 如果请求失败，则返回一个包含错误信息的DtoResult表示操作失败
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsGetPtzResp> getPtz(ThirdPartyPlatformsBaseGetPtzReq basePtzControlReq) {
        // 创建一个VlinkerConvergePtzControlReq对象，用于存储转换后的请求参数
        VlinkerConvergeGetPtzReq req = new VlinkerConvergeGetPtzReq();
        // 设置设备序列号，来源于第三方平台的请求
        req.setDeviceCode(basePtzControlReq.getDeviceCode());
        req.setChannelId(basePtzControlReq.getChannelId());

        // 调用feignSystemApiController.ptzControl方法，发送云台控制请求
        DtoResult<VlinkerConvergeGetPtzResp> dtoResult = feignConvergeSystemApiController.getPtz(req);
        // 如果请求成功，则返回一个空的DtoResult表示操作成功
        if (dtoResult.success()) {
            ThirdPartyPlatformsGetPtzResp resp = new ThirdPartyPlatformsGetPtzResp();

            VlinkerConvergeGetPtzResp data = dtoResult.getData();
            resp.setChannelId(data.getChannelId());
            resp.setPan(data.getPan());
            resp.setTilt(data.getTilt());
            resp.setZoom(data.getZoom());
            return DtoResult.ok(resp);
        }
        // 如果请求失败，则返回一个包含错误信息的DtoResult表示操作失败
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }


    @Override
    public DtoResult<Void> setPtz(ThirdPartyPlatformsBaseSetPtzReq basePtzControlReq) {
        // 创建一个VlinkerConvergePtzControlReq对象，用于存储转换后的请求参数
        VlinkerConvergeSetPtzReq req = new VlinkerConvergeSetPtzReq();
        // 设置设备序列号，来源于第三方平台的请求
        req.setDeviceCode(basePtzControlReq.getDeviceCode());
        req.setChannelId(basePtzControlReq.getChannelId());
        req.setPan(basePtzControlReq.getPan());
        req.setTilt(basePtzControlReq.getTilt());
        req.setZoom(basePtzControlReq.getZoom());
        return feignConvergeSystemApiController.setPtz(req);
    }

    @Override
    public DtoResult<CommonSetStatusLightResp> getFillLightStatus(ThirdPartyPlatformsBaseGetFillLightStatusReq fillLightStatusReq) {
        VlinkerConvergeFillLightStatusReq req = new VlinkerConvergeFillLightStatusReq();
        req.setDeviceCode(fillLightStatusReq.getDeviceCode());
        req.setChannelId(fillLightStatusReq.getChannelId());
        return feignConvergeSystemApiController.getFillLightStatus(req);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsBaseGetFillLightStatusResp> setFillLightStatus(ThirdPartyPlatformsBaseGetFillLightStatusReq fillLightStatusReq) {
        VlinkerConvergeFillLightStatusReq req = new VlinkerConvergeFillLightStatusReq();
        req.setDeviceCode(fillLightStatusReq.getDeviceCode());
        req.setChannelId(fillLightStatusReq.getChannelId());
        req.setMode(fillLightStatusReq.getMode());
        req.setValue(fillLightStatusReq.getValue());
        req.setStatusLight(fillLightStatusReq.getStatusLight());
        DtoResult<VlinkerConvergeFillLightStatusResp> fillLightStatus = feignConvergeSystemApiController.setFillLightStatus(req);
        if (fillLightStatus.success()) {
            ThirdPartyPlatformsBaseGetFillLightStatusResp resp = new ThirdPartyPlatformsBaseGetFillLightStatusResp();
            BeanUtil.copyProperties(fillLightStatus.getData(), resp);
            return DtoResult.ok(resp);
        }
        return DtoResult.error(fillLightStatus.getMessage(), fillLightStatus.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsBaseGetHomePositionResp> getHomePosition(ThirdPartyPlatformsBaseGetHomePositionReq fillLightStatusReq) {
        VlinkerConvergeHomePositionReq req = new VlinkerConvergeHomePositionReq();
        req.setDeviceCode(fillLightStatusReq.getDeviceCode());
        req.setChannelId(fillLightStatusReq.getChannelId());
        DtoResult<VlinkerConvergeHomePositionResp> dtoResult = feignConvergeSystemApiController.getHomePosition(req);
        if (dtoResult.success()) {
            ThirdPartyPlatformsBaseGetHomePositionResp resp = new ThirdPartyPlatformsBaseGetHomePositionResp();
            BeanUtil.copyProperties(dtoResult.getData(), resp);
            return DtoResult.ok(resp);
        }
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsBaseGetHomePositionResp> setHomePosition(ThirdPartyPlatformsBaseSetHomePositionReq fillLightStatusReq) {
        VlinkerConvergeHomePositionReq req = new VlinkerConvergeHomePositionReq();
        req.setDeviceCode(fillLightStatusReq.getDeviceCode());
        req.setChannelId(fillLightStatusReq.getChannelId());
        req.setPresetIndex(fillLightStatusReq.getPresetIndex());
        DtoResult<VlinkerConvergeHomePositionResp> dtoResult = feignConvergeSystemApiController.setHomePosition(req);
        if (dtoResult.success()) {
            ThirdPartyPlatformsBaseGetHomePositionResp resp = new ThirdPartyPlatformsBaseGetHomePositionResp();
            BeanUtil.copyProperties(dtoResult.getData(), resp);
            return DtoResult.ok(resp);
        }
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    /**
     * 实现与第三方平台的云台控制交互。
     * 该方法将接收到的第三方平台的云台控制请求转换为内部统一的请求格式，然后调用相应的接口进行操作。
     * 如果操作成功，则返回一个表示成功的响应；如果操作失败，则返回一个包含错误信息的响应。
     *
     * @param basePtzControlReq 第三方平台的云台控制请求对象，包含了设备代码、操作类型、命令方向、通道ID和速度等信息。
     * @return 返回一个DtoResult对象，其中包含了操作的结果。如果操作成功，则返回一个空的DtoResult；如果操作失败，则返回一个包含错误信息的DtoResult。
     */
    @Override
    public DtoResult<Void> ptzControl(ThirdPartyPlatformsBasePtzControlReq basePtzControlReq) {
        // 创建一个VlinkerConvergePtzControlReq对象，用于存储转换后的请求参数
        VlinkerConvergePtzControlReq req = new VlinkerConvergePtzControlReq();
        // 设置设备序列号，来源于第三方平台的请求
        req.setDeviceCode(basePtzControlReq.getDeviceCode());
        // 设置操作类型，根据第三方平台的请求转换为内部统一的类型
        req.setAction(basePtzControlReq.getAction().getVlinkerConvergeCommand());
        // 设置命令方向，根据第三方平台的请求转换为内部统一的类型
        req.setDirection(basePtzControlReq.getCommand().getVlinkerConvergeCommand());
        // 设置通道ID，来源于第三方平台的请求
        req.setChannelId(basePtzControlReq.getChannelId());
        // 设置速度，来源于第三方平台的请求
        req.setSpeed(basePtzControlReq.getSpeed());

        // 调用feignSystemApiController.ptzControl方法，发送云台控制请求
        DtoResult<Void> dtoResult = feignConvergeSystemApiController.ptzControl(req);
        // 如果请求成功，则返回一个空的DtoResult表示操作成功
        if (dtoResult.success()) {
            return DtoResult.ok();
        }
        // 如果请求失败，则返回一个包含错误信息的DtoResult表示操作失败
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }


    @Override
    public DtoResult<CommonGetRecordMonthResp> getRecordMonth(OpenGetRecordMonthReq req) {
        ConvGetRecordMonthReq convDeviceReq = new ConvGetRecordMonthReq();
        BeanUtils.copyProperties(req, convDeviceReq);
        convDeviceReq.setDeviceCode(req.getDeviceCode());
        convDeviceReq.setChannelId(req.getChannelCode());
        return feignConvergeSystemApiController.getRecordMonth(convDeviceReq);
    }

    /**
     * 获取第三方平台的录像时间线。
     *
     * @param baseVideoBackUrlReq 基础视频回放请求对象，包含设备码、开始时间、结束时间等信息。
     * @return 返回录像时间线的DTO结果，包含第三方平台的录像时间线响应列表。
     */
    @Override
    public DtoResult<List<ThirdPartyPlatformsRecordTimeLineResp>> getRecordTimeLine(ThirdPartyPlatformsBaseRecordTimeLineReq baseVideoBackUrlReq) {
        // 创建Vlinker录像时间线请求对象
        VlinkerRecordTimeLineReq req = new VlinkerRecordTimeLineReq();
        // 设置Vlinker请求对象的属性，基于基础视频回放请求对象中的信息
        req.setDeviceCode(baseVideoBackUrlReq.getDeviceCode());
        req.setStart(baseVideoBackUrlReq.getStart());
        req.setEnd(baseVideoBackUrlReq.getEnd());
        req.setSource(baseVideoBackUrlReq.getBaseVideoBackProtocolEnum().getVlinkerConvergeProtocol());
        req.setChannelId(baseVideoBackUrlReq.getChannelId());
        req.setSsrc(baseVideoBackUrlReq.getSsrc());

        // 调用Feign接口，向系统API控制器请求录像时间线
        DtoResult<List<VlinkerConvergeRecordTimeLineResp>> dtoResult = feignConvergeSystemApiController.getRecordTimeLine(req);

        // 如果请求成功，则将返回的录像时间线列表转换为第三方平台的录像时间线响应列表，并返回
        if (dtoResult.success()) {
            List<ThirdPartyPlatformsRecordTimeLineResp> thirdPartyPlatformsRecordTimeLineResps = new ArrayList<>();
            dtoResult.getData().forEach(e -> {
                ThirdPartyPlatformsRecordTimeLineResp resp = new ThirdPartyPlatformsRecordTimeLineResp();
                resp.setStart(e.getStart());
                resp.setDuration(e.getDuration());
                resp.setId(e.getMediaId());
                resp.setAccessWay(e.getAccessWay());
                thirdPartyPlatformsRecordTimeLineResps.add(resp);
            });
            return DtoResult.ok(thirdPartyPlatformsRecordTimeLineResps);
        }
        // 如果请求失败，则返回错误信息
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    /**
     * 根据第三方平台的视频回看URL请求，获取视频回看URL。
     *
     * @param baseVideoBackUrlReq 第三方平台的视频回看URL请求基础信息，包含设备码、开始时间、结束时间等。
     * @return 返回包含视频回看URL的响应对象，如果请求失败，则包含错误信息。
     */
    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> getVideoBackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
        // 创建Vlinker视频回看请求对象，并设置请求参数
        VlinkerVideoBackReq req = new VlinkerVideoBackReq();
        req.setDeviceSN(baseVideoBackUrlReq.getDeviceCode());
        req.setStart(baseVideoBackUrlReq.getStart());
        req.setEnd(baseVideoBackUrlReq.getEnd());
        req.setSource(baseVideoBackUrlReq.getBaseVideoBackProtocolEnum().getVlinkerConvergeProtocol());
        req.setChannelId(baseVideoBackUrlReq.getChannelId());
        req.setUuid(baseVideoBackUrlReq.getUuid());
        req.setNetworking(baseVideoBackUrlReq.getNetworking());

        // 调用Feign客户端，向系统API控制器发送视频回看URL请求
        DtoResult<VlinkerConvergeVideoBackUrlResp> dtoResult = feignConvergeSystemApiController.getVideoBackUrl(req);

        // 如果请求成功，则返回转换后的第三方平台视频回看URL响应对象，否则返回错误信息
        if (dtoResult.success()) {
            return DtoResult.ok(BeanUtil.copyProperties(dtoResult.getData(), ThirdPartyPlatformsVideoBackUrlResp.class));
        }
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> downloadCloudPlaybackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
        return this.getVideoBackUrl(baseVideoBackUrlReq);
    }

    /**
     * 控制播放的接口方法
     *
     * @param baseControlPlaybackReq 控制播放的请求对象，包含第三方平台的设备码、通道码、缩放比例和UUID等信息。
     * @return 返回控制播放结果的DTO对象，包含操作结果和操作后的播放状态等信息。
     */
    @Override
    public DtoResult<GbControlLocalPlaybackVo> controlPlayback(ThirdPartyPlatformsControlPlaybackReq baseControlPlaybackReq) {
        // 创建Vlinker控制播放请求对象
        VlinkerControlPlaybackReq req = new VlinkerControlPlaybackReq();

        // 设置设备序列号，来源于第三方平台的设备码
        req.setDeviceSn(baseControlPlaybackReq.getDeviceCode());
        // 设置通道ID，来源于第三方平台的通道码
        req.setChannelId(baseControlPlaybackReq.getChannelCode());
        // 设置播放画面的缩放比例
        req.setScale(new BigDecimal(baseControlPlaybackReq.getScale()));
        // 设置UUID，用于标识此次操作的唯一性
        req.setUuid(baseControlPlaybackReq.getUuid());

        // 调用feign客户端，向系统API控制器发送控制播放的请求
        return feignConvergeSystemApiController.controlPlayback(req);
    }

    /**
     * 停止播放操作。
     * <p>
     * 该方法用于向系统发送停止播放的请求，主要针对特定设备和频道。
     * 它首先构造一个具体的停止播放请求对象，然后调用远程API来执行操作。
     * 如果操作成功，它将返回一个成功的空结果；如果操作失败，它将返回一个包含错误信息的结果。
     *
     * @param baseStopPlaybackReq 停止播放的基类请求对象，包含设备代码、频道代码和UUID等信息。
     * @return 返回一个表示操作结果的DtoResult对象，如果成功，则为空；如果失败，则包含错误信息。
     */
    @Override
    public DtoResult<Void> stopPlayback(ThirdPartyPlatformsStopPlaybackReq baseStopPlaybackReq) {
        // 创建停止播放请求对象，并设置设备序列号、频道ID和用户UUID
        VlinkerStopPlaybackReq req = new VlinkerStopPlaybackReq();
        req.setDeviceSn(baseStopPlaybackReq.getDeviceCode());
        req.setChannelId(baseStopPlaybackReq.getChannelCode());
        req.setUuid(baseStopPlaybackReq.getUuid());

        // 调用远程API停止播放，并获取结果
        DtoResult<Void> dtoResult = feignConvergeSystemApiController.stopPlayback(req);

        // 如果操作成功，则返回一个空的成功结果；否则，返回一个包含错误信息的失败结果
        if (dtoResult.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    /**
     * 下载播放记录的方法。
     * <p>
     * 该方法通过调用feignClient中的downloadPlayback方法，实现下载指定设备和频道的播放记录。
     * 它将ThirdPartyPlatformsDownloadPlaybackReq对象的信息转换为VlinkerDownloadPlaybackReq对象，
     * 以便于后续的请求处理。
     *
     * @param baseDownloadPlaybackReq 包含下载播放记录所需基本信息的请求对象。
     *                                deviceCode 设备编号。
     *                                channelCode 频道编号。
     *                                start 开始时间。
     *                                end 结束时间。
     *                                uuid 唯一标识。
     * @return 返回DtoResult<GbDownloadLocalPlaybackVo>对象，其中包含下载播放记录的结果。
     */
    @Override
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(ThirdPartyPlatformsDownloadPlaybackReq baseDownloadPlaybackReq) {
        // 创建VlinkerDownloadPlaybackReq对象，用于存储转换后的请求信息
        VlinkerDownloadPlaybackReq req = new VlinkerDownloadPlaybackReq();
        // 设置设备序列号
        req.setDeviceSn(baseDownloadPlaybackReq.getDeviceCode());
        // 设置频道ID
        req.setChannelId(baseDownloadPlaybackReq.getChannelCode());
        // 设置开始时间
        req.setStart(baseDownloadPlaybackReq.getStart());
        // 设置结束时间
        req.setEnd(baseDownloadPlaybackReq.getEnd());
        // 设置唯一标识
        req.setUuid(baseDownloadPlaybackReq.getUuid());

        // 调用feignClient中的downloadPlayback方法，发起下载播放记录的请求，并返回结果
        return feignConvergeSystemApiController.downloadPlayback(req);
    }

    /**
     * 停止下载回放请求的处理方法。
     * <p>
     * 该方法用于封装并转发停止下载回放的请求，针对特定的设备和频道，根据请求的时间范围执行操作。
     * 请求参数来自于ThirdPartyPlatformsStopDownloadPlaybackReq对象，经过封装后通过Feign客户端发送给系统API控制器。
     *
     * @param baseStopDownloadPlaybackReq 停止下载回放的基类请求对象，包含了设备码、频道码、开始时间、结束时间和UUID等信息。
     * @return 返回一个包含操作结果的DtoResult对象，其中包含了GbStopDownloadLocalPlaybackVo对象，该对象描述了停止下载回放的操作结果。
     */
    @Override
    public DtoResult<GbStopDownloadLocalPlaybackVo> stopDownloadPlayback(ThirdPartyPlatformsStopDownloadPlaybackReq baseStopDownloadPlaybackReq) {
        // 创建VlinkerStopDownloadPlaybackReq对象，用于存储具体的停止下载回放请求参数
        VlinkerStopDownloadPlaybackReq req = new VlinkerStopDownloadPlaybackReq();
        // 设置设备序列号，来源于baseStopDownloadPlaybackReq对象
        req.setDeviceSn(baseStopDownloadPlaybackReq.getDeviceCode());
        // 设置频道ID，来源于baseStopDownloadPlaybackReq对象
        req.setChannelId(baseStopDownloadPlaybackReq.getChannelCode());
        // 设置开始时间，来源于baseStopDownloadPlaybackReq对象
        req.setStart(baseStopDownloadPlaybackReq.getStart());
        // 设置结束时间，来源于baseStopDownloadPlaybackReq对象
        req.setEnd(baseStopDownloadPlaybackReq.getEnd());
        // 设置UUID，来源于baseStopDownloadPlaybackReq对象
        req.setUuid(baseStopDownloadPlaybackReq.getUuid());

        // 调用feignConvergeSystemApiController的stopDownloadPlayback方法，传入封装好的请求对象，并返回结果
        return feignConvergeSystemApiController.stopDownloadPlayback(req);
    }

    /**
     * 获取预置点列表
     *
     * @param basePrePointListReq 第三方平台预置点列表请求基础信息
     * @return 预置点列表响应结果
     * <p>
     * 该方法用于从第三方平台获取预置点列表。首先，它根据请求参数构建一个专门用于请求预置点列表的请求对象。
     * 然后，它调用一个远程服务（通过Feign）来获取预置点列表。如果请求成功，它将响应数据转换为所需的响应对象类型，
     * 并返回这个转换后的列表。如果请求失败，则返回错误信息。
     */
    @Override
    public DtoResult<List<ThirdPartyPlatformsPrePointListResp>> getPrePointList(ThirdPartyPlatformsBasePrePointListReq basePrePointListReq) {
        // 创建请求对象，并设置设备SN和通道ID
        VlinkerPreSetListReq req = new VlinkerPreSetListReq();
        req.setDeviceCode(basePrePointListReq.getDeviceCode());
        req.setChannelId(basePrePointListReq.getChannelId());

        // 调用Feign客户端，请求预置点列表
        DtoResult<List<VlinkerConvergePreSetListResp>> vlinkerConvergeBaseResp = feignConvergeSystemApiController.getPreSetList(req);

        // 如果请求成功，转换响应数据并返回
        if (vlinkerConvergeBaseResp.success()) {
            List<ThirdPartyPlatformsPrePointListResp> thirdPartyPlatformsPrePointListResps = BeanUtil.copyToList(vlinkerConvergeBaseResp.getData(), ThirdPartyPlatformsPrePointListResp.class);
            return DtoResult.ok(thirdPartyPlatformsPrePointListResps);
        }
        // 如果请求失败，返回错误信息
        return DtoResult.error(vlinkerConvergeBaseResp.getMessage(), vlinkerConvergeBaseResp.getError());
    }

    /**
     * 设置预置点方法
     *
     * @param baseSetPrePointReq 设置预置点的请求参数，包含设备代码、索引、名称和通道ID等信息。
     * @return 返回设置预置点的结果，成功时返回空结果，失败时返回错误信息。
     * <p>
     * 该方法通过将请求参数封装成VlinkerSetPreSetReq对象，然后调用feignConvergeSystemApiController的setPreSet方法来设置预置点。
     * 如果设置成功，则返回一个成功的空结果；如果设置失败，则返回包含错误信息的结果。
     */
    @Override
    public DtoResult<Void> setPrePoint(ThirdPartyPlatformsBaseSetPrePointReq baseSetPrePointReq) {
        // 创建VlinkerSetPreSetReq对象，并设置设备序列号、索引、名称和通道ID等信息
        VlinkerSetPreSetReq req = new VlinkerSetPreSetReq();
        req.setDeviceCode(baseSetPrePointReq.getDeviceCode());
        req.setIndex(baseSetPrePointReq.getIndex());
        req.setName(baseSetPrePointReq.getName());
        req.setChannelId(baseSetPrePointReq.getChannelId());

        // 调用feignConvergeSystemApiController的setPreSet方法来设置预置点，并将结果保存在vlinkerConvergeBaseResp中
        DtoResult<Void> vlinkerConvergeBaseResp = feignConvergeSystemApiController.setPreSet(req);

        // 如果设置成功，则返回一个成功的空结果；如果设置失败，则返回包含错误信息的结果
        if (vlinkerConvergeBaseResp.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(vlinkerConvergeBaseResp.getMessage(), vlinkerConvergeBaseResp.getError());
    }

    @Override
    public DtoResult<Void> jumpPrePoint(ThirdPartyPlatformsBaseJumpPrePointReq baseJumpPrePointReq) {
        VlinkerJumpPreSetReq req = new VlinkerJumpPreSetReq();
        req.setDeviceCode(baseJumpPrePointReq.getDeviceCode());
        req.setIndex(baseJumpPrePointReq.getIndex());
        req.setChannelId(baseJumpPrePointReq.getChannelId());
        DtoResult<Void> vlinkerConvergeBaseResp = feignConvergeSystemApiController.jumpPreSet(req);
        if (vlinkerConvergeBaseResp.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(vlinkerConvergeBaseResp.getMessage(), vlinkerConvergeBaseResp.getError());
    }

    @Override
    public DtoResult<Void> deletePrePoint(ThirdPartyPlatformsBaseDeletePrePointReq baseDeletePrePointReq) {
        VlinkerDelPreSetReq req = new VlinkerDelPreSetReq();
        req.setDeviceCode(baseDeletePrePointReq.getDeviceCode());
        req.setIndex(baseDeletePrePointReq.getIndex());
        req.setChannelId(baseDeletePrePointReq.getChannelId());
        DtoResult<Void> vlinkerConvergeBaseResp = feignConvergeSystemApiController.delPreSet(req);
        if (vlinkerConvergeBaseResp.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(vlinkerConvergeBaseResp.getMessage(), vlinkerConvergeBaseResp.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsRtcConnectResp> rtcConnect(ThirdPartyPlatformsBaseRtcConnectReq baseRtcConnectReq) {
        VlinkerRtcConnectReq req = new VlinkerRtcConnectReq();
        req.setDeviceCode(baseRtcConnectReq.getDeviceCode());
        req.setChannelId(baseRtcConnectReq.getChannelId());
        req.setPlayType(baseRtcConnectReq.getPlayType());
        DtoResult<VlinkerConvergeRtcConnectResp> dtoResult = feignConvergeSystemApiController.rtcConnect(req);
        if (dtoResult.success()) {
            ThirdPartyPlatformsRtcConnectResp thirdPartyPlatformsRtcConnectResp = new ThirdPartyPlatformsRtcConnectResp();
            thirdPartyPlatformsRtcConnectResp.setWsUrl(dtoResult.getData().getWsUrl());
            thirdPartyPlatformsRtcConnectResp.setSdUrl(dtoResult.getData().getSdUrl());
            thirdPartyPlatformsRtcConnectResp.setSessionId(dtoResult.getData().getSessionId());
            thirdPartyPlatformsRtcConnectResp.setChannelId(dtoResult.getData().getChannelId());
            thirdPartyPlatformsRtcConnectResp.setTurn(dtoResult.getData().getTurn());
            thirdPartyPlatformsRtcConnectResp.setStun(dtoResult.getData().getStun());
            thirdPartyPlatformsRtcConnectResp.setTransport(dtoResult.getData().getTransport());
            if ("rtc".equals(dtoResult.getData().getType())) {
                thirdPartyPlatformsRtcConnectResp.setType(2);
            } else if ("gb".equals(dtoResult.getData().getType())) {
                thirdPartyPlatformsRtcConnectResp.setType(3);
            } else if ("sd".equals(dtoResult.getData().getType())) {
                thirdPartyPlatformsRtcConnectResp.setType(4);
            }
            return DtoResult.ok(thirdPartyPlatformsRtcConnectResp);
        }
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsSnapshotResp> snapshot(ThirdPartyPlatformsBaseSnapshotReq baseSnapshotReq) {
        DeviceSnapshotSuperReq req = new DeviceSnapshotSuperReq();
        req.setDeviceCode(baseSnapshotReq.getDeviceCode());
        req.setChannelId(baseSnapshotReq.getChannelId());
        req.setTimes(baseSnapshotReq.getTimes());
        req.setWidth(baseSnapshotReq.getWidth());
        req.setHeight(baseSnapshotReq.getHeight());
        req.setGenerateFlag(baseSnapshotReq.getGenerateFlag());
        DtoResult<VlinkerConvergeSnapshotResp> snapshot = feignConvergeSystemApiController.snapshot(req);
        if (snapshot.success()) {
            ThirdPartyPlatformsSnapshotResp thirdPartyPlatformsSnapshotResp = BeanUtil.copyProperties(snapshot.getData(), ThirdPartyPlatformsSnapshotResp.class);
            return DtoResult.ok(thirdPartyPlatformsSnapshotResp);
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }


    @Override
    public DtoResult<ThirdPartyPlatformsQuerySupportPrecisePtzResp> querySupportPrecisePtz(ThirdPartyPlatformsQuerySupportPrecisePtzReq dto) {
        return feignConvergeSystemApiController.querySupportPrecisePtz(dto);
    }


    @Override
    public DtoResult<ThirdPartyPlatformsFlipVideoResp> flipVideo(ThirdPartyPlatformsFlipVideoReq flipVideoReq) {
        VlinkerFlipVideoReq req = new VlinkerFlipVideoReq();
        req.setDeviceCode(flipVideoReq.getDeviceCode());
        req.setChannelId(flipVideoReq.getChannelId());
        req.setFrameMirror(flipVideoReq.getFrameMirror());
        DtoResult<VlinkerConvergeFlipVideoResp> snapshot = feignConvergeSystemApiController.flipVideo(req);
        if (snapshot.success()) {
            ThirdPartyPlatformsFlipVideoResp thirdPartyPlatformsSnapshotResp = BeanUtil.copyProperties(snapshot.getData(), ThirdPartyPlatformsFlipVideoResp.class);
            return DtoResult.ok(thirdPartyPlatformsSnapshotResp);
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsFlipVideoResp> getFlipVideo(ThirdPartyPlatformsFlipVideoReq flipVideoReq) {
        VlinkerFlipVideoReq req = new VlinkerFlipVideoReq();
        req.setDeviceCode(flipVideoReq.getDeviceCode());
        req.setChannelId(flipVideoReq.getChannelId());
        DtoResult<VlinkerConvergeFlipVideoResp> snapshot = feignConvergeSystemApiController.getFlipVideo(req);
        if (snapshot.success()) {
            ThirdPartyPlatformsFlipVideoResp thirdPartyPlatformsSnapshotResp = BeanUtil.copyProperties(snapshot.getData(), ThirdPartyPlatformsFlipVideoResp.class);
            return DtoResult.ok(thirdPartyPlatformsSnapshotResp);
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<ThirdPartyPlatformsSdCardInfoResp> getSdCardInfo(ThirdPartyPlatformsSdCardInfoReq sdCardInfoReq) {
        VlinkerSdCardInfoReq req = new VlinkerSdCardInfoReq();
        req.setDeviceCode(sdCardInfoReq.getDeviceCode());
        req.setChannelId(sdCardInfoReq.getChannelId());
        DtoResult<ConvergeSdCardCapacityResp> snapshot = feignConvergeSystemApiController.getSdCardInfo(req);
        if (snapshot.success()) {
            ThirdPartyPlatformsSdCardInfoResp thirdPartyPlatformsSnapshotResp = new ThirdPartyPlatformsSdCardInfoResp();
            thirdPartyPlatformsSnapshotResp.setNum(snapshot.getData().getNum());
            thirdPartyPlatformsSnapshotResp.setItems(snapshot.getData().getItems().stream().map(item -> {
                ThirdPartyPlatformsSdCardInfoResp.SDCardStatus sdCardStatus = new ThirdPartyPlatformsSdCardInfoResp.SDCardStatus();
                sdCardStatus.setId(item.getId());
                sdCardStatus.setHddName(item.getHddName());
                sdCardStatus.setStatus(item.getStatus());
                sdCardStatus.setFormatProgress(item.getFormatProgress());
                sdCardStatus.setCapacity(item.getCapacity());
                sdCardStatus.setFreeSpace(item.getFreeSpace());
                return sdCardStatus;
            }).collect(Collectors.toList()));
            return DtoResult.ok(thirdPartyPlatformsSnapshotResp);
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<CommonChannelAbilityResp> getChannelAbility(OpenDeviceReq openDeviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openDeviceReq.getDeviceCode());
        convDeviceReq.setChannelId(openDeviceReq.getChannelCode());
        return feignConvergeSystemApiController.getChannelAbility(convDeviceReq);
    }

    @Override
    public DtoResult<Void> sdCardInfoFormat(ThirdPartyPlatformsSdCardInfoReq sdCardInfoReq) {
        VlinkerSdCardInfoReq req = new VlinkerSdCardInfoReq();
        req.setDeviceCode(sdCardInfoReq.getDeviceCode());
        req.setChannelId(sdCardInfoReq.getChannelId());
        DtoResult<Void> snapshot = feignConvergeSystemApiController.sdCardInfoFormat(req);
        if (snapshot.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<CommonGetSideAlgorithmResp> getSideAlgorithmInfo(OpenGetSideAlgorithmReq dto) {
        ConvGetSideAlgorithmReq convDeviceReq = new ConvGetSideAlgorithmReq();
        BeanUtils.copyProperties(dto, convDeviceReq);
        convDeviceReq.setDeviceCode(dto.getDeviceCode());
        convDeviceReq.setChannelId(dto.getChannelCode());
        return feignConvergeSystemApiController.getSideAlgorithmInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonSetSideAlgorithmResp> setSideAlgorithmInfo(OpenSetSideAlgorithmReq dto) {
        ConvSetSideAlgorithmReq convDeviceReq = new ConvSetSideAlgorithmReq();
        BeanUtils.copyProperties(dto, convDeviceReq);
        convDeviceReq.setDeviceCode(dto.getDeviceCode());
        convDeviceReq.setChannelId(dto.getChannelCode());
        return feignConvergeSystemApiController.setSideAlgorithmInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> getNowDeviceVersionInfo(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.getNowDeviceVersionInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetDeviceVersionListResp> getNowDeviceVersionListInfo(OpenDevicePageReq deviceReq) {
        ConvDevicePageReq convDeviceReq = new ConvDevicePageReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        convDeviceReq.setPageNum(deviceReq.getPageNum());
        convDeviceReq.setPageSize(deviceReq.getPageSize());
        return feignConvergeSystemApiController.getNowDeviceVersionListInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.deviceUpgrade(convDeviceReq);
    }

    @Override
    public DtoResult<Void> deviceRestart(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.deviceRestart(convDeviceReq);
    }

    @Override
    public DtoResult<Void> deviceReset(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.deviceReset(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.getOsdInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(OpenCallDeviceByVideoReq deviceReq) {
        ConvCallDeviceByVideoReq convDeviceReq = new ConvCallDeviceByVideoReq();
        BeanUtils.copyProperties(deviceReq, convDeviceReq);
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.callDeviceByVideo(convDeviceReq);
    }

    @Override
    public DtoResult<Void> voiceStatusNotifyReq(OpenVoiceStatusNotifyReq deviceReq) {
        ConvVoiceStatusNotifyReq convDeviceReq = new ConvVoiceStatusNotifyReq();
        BeanUtils.copyProperties(deviceReq, convDeviceReq);
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.voiceStatusNotifyReq(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(OpenSetOsdInfoReq deviceReq) {
        ConvSetOsdInfoReq convDeviceReq = new ConvSetOsdInfoReq();
        BeanUtils.copyProperties(deviceReq, convDeviceReq);
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.setOsdInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetDormancyResp> getDormancyParameter(OpenDeviceReq deviceReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.getDormancyParameter(convDeviceReq);
    }

    @Override
    public DtoResult<Void> stopCruiseTrack(ThirdPartyPlatformsCruiseTrackReq cruiseTrackReq) {
        VlinkerCruiseTrackReq req = new VlinkerCruiseTrackReq();
        req.setDeviceCode(cruiseTrackReq.getDeviceCode());
        req.setChannelId(cruiseTrackReq.getChannelId());
        req.setNumber(cruiseTrackReq.getNumber());
        DtoResult<Void> snapshot = feignConvergeSystemApiController.stopCruiseTrack(req);
        if (snapshot.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<Void> delCruiseTrack(ThirdPartyPlatformsCruiseTrackReq cruiseTrackReq) {
        VlinkerCruiseTrackReq req = new VlinkerCruiseTrackReq();
        req.setDeviceCode(cruiseTrackReq.getDeviceCode());
        req.setChannelId(cruiseTrackReq.getChannelId());
        req.setNumber(cruiseTrackReq.getNumber());
        DtoResult<Void> snapshot = feignConvergeSystemApiController.delCruiseTrack(req);
        if (snapshot.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<Void> startCruiseTrack(ThirdPartyPlatformsCruiseTrackReq cruiseTrackReq) {
        VlinkerCruiseTrackReq req = new VlinkerCruiseTrackReq();
        req.setDeviceCode(cruiseTrackReq.getDeviceCode());
        req.setChannelId(cruiseTrackReq.getChannelId());
        req.setNumber(cruiseTrackReq.getNumber());
        DtoResult<Void> snapshot = feignConvergeSystemApiController.startCruiseTrack(req);
        if (snapshot.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrackList(ThirdPartyPlatformsCruiseTrackReq cruiseTrackReq) {
        VlinkerCruiseTrackReq req = new VlinkerCruiseTrackReq();
        req.setDeviceCode(cruiseTrackReq.getDeviceCode());
        req.setChannelId(cruiseTrackReq.getChannelId());
        DtoResult<VlinkerConvergeCruiseTrackResp> snapshot = feignConvergeSystemApiController.getCruiseTrackList(req);
        if (snapshot.success()) {
            return DtoResult.ok(snapshot.getData());
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrack(ThirdPartyPlatformsCruiseTrackReq cruiseTrackReq) {
        VlinkerCruiseTrackReq req = new VlinkerCruiseTrackReq();
        req.setDeviceCode(cruiseTrackReq.getDeviceCode());
        req.setChannelId(cruiseTrackReq.getChannelId());
        req.setNumber(cruiseTrackReq.getNumber());
        DtoResult<VlinkerConvergeCruiseTrackResp> snapshot = feignConvergeSystemApiController.getCruiseTrack(req);
        if (snapshot.success()) {
            return DtoResult.ok(snapshot.getData());
        }
        return DtoResult.error(snapshot.getMessage(), snapshot.getError());
    }

    @Override
    public DtoResult<SaveOrUpdateCruiseTrackResp> saveOrUpdateCruiseTrack(OpenSaveOrUpdateCruiseTrackReq req) {
        ConvSaveOrUpdateCruiseTrackReq convDeviceReq = new ConvSaveOrUpdateCruiseTrackReq();
        BeanUtils.copyProperties(req, convDeviceReq);
        convDeviceReq.setDeviceCode(req.getDeviceCode());
        convDeviceReq.setChannelId(req.getChannelCode());
        return feignConvergeSystemApiController.saveOrUpdateCruiseTrack(convDeviceReq);
    }


    @Override
    public DtoResult<CommonSetDormancyResp> setDormancyParameter(OpenSetDormancyParameterReq deviceReq) {
        ConvSetDormancyParameterReq convDeviceReq = new ConvSetDormancyParameterReq();
        BeanUtils.copyProperties(deviceReq, convDeviceReq);
        convDeviceReq.setDeviceCode(deviceReq.getDeviceCode());
        convDeviceReq.setChannelId(deviceReq.getChannelCode());
        return feignConvergeSystemApiController.setDormancyParameter(convDeviceReq);
    }


    @Override
    public DtoResult<Void> setDualCameraLinkage(OpenSetDualCameraLinkageReq openReq) {
        ConvSetDualCameraLinkageReq convDeviceReq = new ConvSetDualCameraLinkageReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setDualCameraLinkage(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setSoundAndLightShock(OpenSetSoundAndLightShockReq openReq) {
        ConvSetSoundAndLightShockReq convDeviceReq = new ConvSetSoundAndLightShockReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setSoundAndLightShock(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetSoundAndLightShockReq> getSoundAndLightShock(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.getSoundAndLightShock(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setVolumeCommand(OpenSetVolumeCommandReq openReq) {
        ConvSetVolumeCommandReq convDeviceReq = new ConvSetVolumeCommandReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setVolumeCommand(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetVolumeCommandResp> getVolumeCommand(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.getVolumeCommand(convDeviceReq);
    }

    @Override
    public DtoResult<Void> oneClickPatrol(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.oneClickPatrol(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setHumanoidMarkers(OpenSetHumanoidMarkersReq openReq) {
        ConvSetHumanoidMarkersReq convDeviceReq = new ConvSetHumanoidMarkersReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setHumanoidMarkers(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetHumanoidMarkersReq> getHumanoidMarkers(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.getHumanoidMarkers(convDeviceReq);
    }

    @Override
    public DtoResult<Void> gimbalCalibration(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.gimbalCalibration(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setDevicRelativeXyz(OpenSetDevicRelativeXyzReq openReq){
        ConvSetDevicRelativeXyzReq convDeviceReq = new ConvSetDevicRelativeXyzReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setDevicRelativeXyz(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetScreenPropertiesResp> getScreenInfo(OpenDeviceReq openReq) {
        ConvDeviceReq convDeviceReq = new ConvDeviceReq();
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.getScreenInfo(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setScreenInfo(OpenSetScreenPropertiesReq openReq) {
        ConvSetScreenPropertiesReq convDeviceReq = new ConvSetScreenPropertiesReq();
        BeanUtils.copyProperties(openReq, convDeviceReq);
        convDeviceReq.setDeviceCode(openReq.getDeviceCode());
        convDeviceReq.setChannelId(openReq.getChannelCode());
        return feignConvergeSystemApiController.setScreenInfo(convDeviceReq);
    }
}