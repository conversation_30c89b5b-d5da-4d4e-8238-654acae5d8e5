server:
  port: 7005
  servlet:
    context-path: /converge-system

spring:
  servlet:
    multipart:
      enabled: true
      max-request-size: 1024MB
      max-file-size: 1024MB
  main:
    allow-bean-definition-overriding: true
  # 数据源配置
  datasource:
    url: *********************************************************************************************************************************************
    username: zhangjinchao
    password: GUHv4VrTF9dABsxQuaS7
    driver-class-name: com.mysql.cj.jdbc.Driver

    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 客户端等待连接池连接的最大毫秒数
      connection-timeout: 30000
      # 池中维护的最小空闲连接数 minIdle<0 或者 minIdle>maxPoolSize，则被重置为maxPoolSize
      minimum-idle: 10
      # 配置最大池大小
      maximum-pool-size: 65
      # 允许连接在连接池中空闲的最长时间（以毫秒为单位）
      idle-timeout: 60000
      # 池中连接关闭后的最长生命周期（以毫秒为单位）
      max-lifetime: 500000
      # 每5分钟发送一次 ping
      keepalive-time: 300000
      # 配置从池返回的连接的默认自动提交行为。默认值为true。
      auto-commit: true
      # 连接池的名称
      pool-name: VlinkerHikariCP
      # 开启连接监测泄露
      leak-detection-threshold: 5000
      # 测试连接数据库
      connection-test-query: SELECT 1

  redis:
    host: *************
    port: 6379
    database: 11
    password: xX*U5SBWU=zbYDE?8zdu
    timeout: 10000
    jedis:
      pool:
        min-idle: 10
        max-active: 30

  jackson:
    default-property-inclusion: non_null
    generator:
      write-numbers-as-strings: true

  sleuth:
    enabled: true
mybatis-plus:
  configuration:
    ## mybatis不输出sql语句
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  type-aliases-package: com.saida.services.system.**.entity
  mapper-locations: classpath:/mapper/**/*Mapper.xml

sysconfig:
  uploadPath: v_file

#对象存储服务 主要是版本升级用的
#oss:
#  region: xiongan
#  accessKey: 0f6ca7429abaee658d82
#  secretKey: 7628ce52ec8f905603002d16d58a84a58f2a23e0
#  endPoint: https://oos-xiongan.ctyunapi.cn
#  bucket: apkbao

#告警图片存储
s3-data:
  enable: true
  type: 1
  region: dev
  accessKey: SaidaMinio
  secretKey: Saida@Minio123
  # endPoint 最后的/ 去掉
  endPoint: https://dev-minio-api.sdccx.cn:58801
  returnPoint: https://dev-minio-api.sdccx.cn:58801
  bucket: converge-alarm
  # 设置对应的生命周期 实际使用的时候 请对应填写前缀！！
  rules:
    - prefix: alarm
      expirationInDays: 1


xxl:
  job:
    enabled: true
    accessToken: default_token
    admin:
      addresses: http://127.0.0.1:12003/xxl-job-admin
    executor:
      address: ''
      appname: ${spring.application.name}
      ip: ''
      logpath: logs/console
      logretentiondays: 7
      port: 0
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100


# https://docs.redpanda.com/current/shared/_attachments/redpanda-console-config.yaml
kafka:
  enable: false
#  bootstrapServers: **************:19092
  bootstrapServers: ************:9092
  replicationFactor: 1

rocketmq:
  enable: true
  enhance:
    # 启动隔离，用于激活配置类EnvironmentIsolationConfig
    # 启动后会自动在topic上拼接激活的配置文件，达到自动隔离的效果
    enabledIsolation: false
    # 隔离环境名称，拼接到topic后，topic_dev，默认空字符串
    environment: ${spring.profiles.active}
  consumer:
    group: springboot_consumer_group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  #  name-server: **********:9876 #测试环境
  name-server: **************:9876  #zhouxun环境
  # 如果说你连不上这个环境 那就不要启动这个服务 影响别人调试！
  #  name-server: 192.168.80.199:9876  #tp环境
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: springboot_producer_group
    # 发送消息超时时间，默认3000
    sendMessageTimeout: 10000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 2
    # 异步消息重试此处，默认2
    retryTimesWhenSendAsyncFailed: 2
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    #    maxMessageSize: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compressMessageBodyThreshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retryNextServer: false

ops:
  host-data:
    #主机标识
    host-name:
      - Linux
      - linux
      - win
    #中间件标识
    middleware-name:
      - middleware
      #普罗米修斯api
    prometheusApi: http://*************:9090/api/v1/query

sms:
  signature: 【云视界】

sun:
  enable: true
  confu:
    inLocalAreaNetwork: true


sdk:
  dahua:
    enable: false
  airui:
    enable: false
    # lib 文件夹路径
    lib-path: /home/<USER>/project/sdkFile/airui-sdk/libs
    # sdk 文件夹路径
    sdk-path: /home/<USER>/project/vlinkeropen/doc/airui-sdk/rust_ircnetsdk


# 1400 服务
one-four-zero:
  enable: false
  # 结尾带上斜杠
  host: http://**************:11400/
  token: Basic YWRtaW46OGM2OTc2ZTViNTQxMDQxNWJkZTkwOGJkNGRlZTE1ZGZiMTY3YTljODczZmM0YmI4YTgxZjZmMmFiNDQ4YTkxOA==

# 巨龙设备mqtt通信 版本：5.8.4
ju-long-mqtt:
  enable: false
  broker: tcp://**************:1883
  username: admin
  password: zjc741209

#英特灵达用的mqtt通信
mqtt:
  enabled: false
  url: tcp://**********:1883
  clientId: vlinker_algorithm_local
  topics: intellindust/edgebox/+/all/post
  username: vlinker_algorithm_local
  password: vlinker_algorithm_local
gdal:
  enable: false
  gdalExePath: /Users/<USER>/CLionProjects/gdal-cmd/cmake-build-debug/gdal_cmd
  dem:
    - code: anhui
      path: /Users/<USER>/Downloads/tif/anhui.tif

tts:
  enable: true
  url: http://127.0.0.1:8000
