package com.saida.services.system.video.service.impl.v2;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.protobuf.ByteString;
import com.google.protobuf.Timestamp;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.oss.OSSBean;
import com.saida.services.common.config.oss.OSSUtil;
import com.saida.services.common.config.oss.S3ObjectDto;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.converge.qxNode.QxNodeCmdType;
import com.saida.services.converge.qxNode.req.sd.ConvergeControlPlaybackReq;
import com.saida.services.converge.qxNode.resp.ChannelRecordTimeLine;
import com.saida.services.converge.qxNode.resp.ChannelRecordUrlResp;
import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
import com.saida.services.deviceApi.req.CommonCallDeviceByVideoReq;
import com.saida.services.deviceApi.req.CommonStopPlayBackReq;
import com.saida.services.deviceApi.resp.CommonCallDeviceByVideoResp;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.resp.VlinkerConvergeAddDeviceResp;
import com.saida.services.open.resp.VlinkerConvergeSnapshotResp;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.client.nodev2.SunConfig;
import com.saida.services.system.client.nodev2.SunGrpcUtil;
import com.saida.services.system.nodeGrpc.GrpcConfig;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.pb.*;
import com.saida.services.system.sys.service.ConvSysOrgService;
import com.saida.services.system.video.algHall.AlgHall;
import com.saida.services.system.video.algHall.AlgRoom;
import com.saida.services.system.video.indexer.IndexFileParser;
import com.saida.services.system.video.indexer.MediaIndexerDataBufferInfo;
import com.saida.services.system.video.indexer.TimelineBuilder;
import com.saida.services.system.video.param.*;
import com.saida.services.system.video.service.VideoStreamService;
import io.grpc.ManagedChannel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@Slf4j
@Service
public class VideoNodeV2StreamServiceImpl implements VideoStreamService {

    @Resource
    private DeviceService deviceService;
    @Resource
    private SignalNodeService signalNodeService;
    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Resource
    private CloudStorageService cloudStorageService;
    @Resource
    private OSSUtil ossUtil;
    @Resource
    private GrpcConfig grpcConfig;
    @Resource
    private ConvSysOrgService convSysOrgService;

    @Getter
    private static final String platformId = "conv";
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private FileService fileService;

    // 验证返回状态码
    private void checkStatus(OpenCommonEnum.ReplyStatus replyStatus) {
        switch (replyStatus) {
            case REPLY_STATUS_SUC:
                break;
            case REPLY_STATUS_UNKNOWN:
                throw new BizRuntimeException("响应未知错误");
            case REPLY_STATUS_ERROR_INVALID_ARGS:
                throw new BizRuntimeException("响应参数错误");
            case REPLY_STATUS_ERROR_SIGNALING_PLANET_NOT_FOUND:
                throw new BizRuntimeException("节点不存在");
            case REPLY_STATUS_ERROR_DEVICE_NOT_FOUND:
                throw new BizRuntimeException("设备不存在");
            case REPLY_STATUS_ERROR_DEVICE_OFFLINE:
                throw new BizRuntimeException("设备离线");
            case REPLY_STATUS_ERROR_CHANNEL_OFFLINE:
                throw new BizRuntimeException("通道离线");
            case REPLY_STATUS_ERROR_DEVICE_RESPONSE_CODE_NOT_OK:
                throw new BizRuntimeException("设备响应错误");
            case REPLY_STATUS_ERROR_INVALID_NETWORK_PROTOCOL:
                throw new BizRuntimeException("无效的网络协议");
            case REPLY_STATUS_ERROR_MESSAGE_TYPE_NOT_SUPPORTED:
                throw new BizRuntimeException("不支持的消息类型");
            case REPLY_STATUS_ERROR_DEVICE_ERROR_CODE_NOT_OK:
                throw new BizRuntimeException("设备错误");
            case REPLY_STATUS_ERROR_TBD:
                throw new BizRuntimeException("TBD");
            case REPLY_STATUS_FATAL_INTERNAL_PARSING:
            case REPLY_STATUS_FATAL_INTERNAL_MARSHALING:
            case REPLY_STATUS_FATAL_INTERNAL_COORDINATION:
            case REPLY_STATUS_FATAL_INTERNAL_DATABASE:
            case REPLY_STATUS_FATAL_INTERNAL_PEER_ANALYZING:
            case REPLY_STATUS_FATAL_INTERNAL_DEVICE_RESPONSE_CAN_NOT_READ:
            case REPLY_STATUS_FATAL_INTERNAL_PLANET_NOT_FOUND:
            case REPLY_STATUS_FATAL_INTERNAL_PLANET_MANAGER_NOT_FOUND:
            case REPLY_STATUS_FATAL_INTERNAL_REAUTHENTICATION_FAILED:
            case REPLY_STATUS_FATAL_NOT_IMPL:
        }
    }

    /**
     * Adds a device to the system based on its access way protocol.
     *
     * @param entity     the device entity to be added
     * @param signalNode the signal node entity
     * @return the result of the add device operation
     */
    @Override
    public DtoResult<VlinkerConvergeAddDeviceResp> addDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        // Check if the device access way is supported
        if (entity.getAccessWay() != 2 && entity.getAccessWay() != 5 && entity.getAccessWay() != 1) {
            return DtoResult.error("当前节点暂不支持此协议接入！");
        }

        OpenSunSaida.AddCommonDeviceReply addDeviceReply;
        // Determine the device protocol and perform the device addition
        switch (entity.getAccessWay()) {
            case 1:
                ConvSysOrgEntity convSysOrg = convSysOrgService.getById(entity.getOrgId());
                if (convSysOrg == null) {
                    return DtoResult.error("组织不存在");
                }
                OpenSunSaida.GBDeviceAddExt ext = OpenSunSaida.GBDeviceAddExt.newBuilder()
                        .setPrefer(entity.getPassword())
                        .setCheckPassword(true)
                        .build();
                try {
                    ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
                    addDeviceReply = sunOpenBlockingStub.addCommonDevice(OpenSunSaida.AddCommonDeviceRequest.newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setDeviceId(entity.getDeviceCode())
                            .setPlatformId(convSysOrg.getGbCode())
                            .setPlatformType(OpenCommonEnum.PlatformType.PLATFORM_TYPE_GB)
                            .setName(entity.getName())
                            .setExt(ByteString.copyFrom(ext.toByteArray()))
                            .build());
                } catch (Exception e) {
                    log.error("设备新增失败！", e);
                    return DtoResult.error("设备新增失败！");
                }
                break;
            case 2:
                try {
                    ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
                    addDeviceReply = sunOpenBlockingStub.addCommonDevice(OpenSunSaida.AddCommonDeviceRequest.newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setDeviceId(entity.getDeviceCode())
                            .setPlatformId(platformId)
                            .setPlatformType(OpenCommonEnum.PlatformType.PLATFORM_TYPE_SAIDA)
                            .setName(entity.getName())
                            .build());
                } catch (Exception e) {
                    log.error("设备新增失败！", e);
                    return DtoResult.error("设备新增失败！");
                }
                break;
            case 5:
                try {
                    ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
                    addDeviceReply = sunOpenBlockingStub.addCommonDevice(OpenSunSaida.AddCommonDeviceRequest.newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setDeviceId(entity.getDeviceCode())
                            .setPlatformId(platformId)
                            .setPlatformType(OpenCommonEnum.PlatformType.PLATFORM_TYPE_HIK_E_HOME_ISUP)
                            .setName(entity.getName())
                            .build());
                } catch (Exception e) {
                    log.error("设备新增失败！", e);
                    return DtoResult.error("设备新增失败！");
                }
                break;
            default:
                return DtoResult.error("当前节点暂不支持此协议接入！");
        }
        if (addDeviceReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            VlinkerConvergeAddDeviceResp resp = new VlinkerConvergeAddDeviceResp();
            resp.setNodeXId(addDeviceReply.getXId());
            resp.setAuthKey(addDeviceReply.getAuthKey());
            resp.setSignalingAddr(addDeviceReply.getHost() + ":" + addDeviceReply.getPort());
            return DtoResult.ok(resp);
        } else {
            return DtoResult.error("设备新增失败！", addDeviceReply.getDesc());
        }
    }

    /**
     * Helper method to handle device addition for a specific protocol.
     *
     * @param entity       the device entity
     * @param signalNode   the signal node entity
     * @param platformType the platform type for the device
     * @return the result of the add device operation
     */
    private DtoResult<VlinkerConvergeAddDeviceResp> addDeviceWithProtocol(String pid, DeviceEntity entity, SignalNodeEntity signalNode, OpenCommonEnum.PlatformType platformType) {
        OpenSunSaida.AddCommonDeviceReply addDeviceReply;
        try {
            ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
            addDeviceReply = sunOpenBlockingStub.addCommonDevice(OpenSunSaida.AddCommonDeviceRequest.newBuilder()
                    .setAuthInfo(grpcConfig.getAuthUser())
                    .setDeviceId(entity.getDeviceCode())
                    .setPlatformId(pid)
                    .setPlatformType(platformType)
                    .setName(entity.getName())
                    .build());
        } catch (Exception e) {
            log.error("设备新增失败！", e);
            return DtoResult.error("设备新增失败！");
        }
        if (addDeviceReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            VlinkerConvergeAddDeviceResp resp = new VlinkerConvergeAddDeviceResp();
            resp.setNodeXId(addDeviceReply.getXId());
            resp.setAuthKey(addDeviceReply.getAuthKey());
            if (platformType != OpenCommonEnum.PlatformType.PLATFORM_TYPE_HIK_E_HOME_ISUP) {
                resp.setSignalingAddr(addDeviceReply.getHost() + ":" + addDeviceReply.getPort());
            }
            return DtoResult.ok(resp);
        } else {
            return DtoResult.error("设备新增失败！", addDeviceReply.getDesc());
        }
    }

    @Override
    public DtoResult<VlinkerConvergeAddDeviceResp> delDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        if (entity.getNodeXId() == null) {
            return DtoResult.ok();
        }
        OpenSun.StatefulReply baseReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(entity.getNodeXId());
            ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
            baseReply = sunOpenBlockingStub.deviceOperation(OpenSunMessage.DeviceOperationRequest.newBuilder()
                    .setAuthInfo(authInfo)
                    .setXId(entity.getNodeXId())
                    .setOperation(OpenSunMessage.DeviceOperationRequest.DeviceOperation.DEVICE_OPERATION_DELETE)
                    .build());
        } catch (Exception e) {
            log.error("设备删除失败！xid:{}", entity.getNodeXId(), e);
            return DtoResult.error("设备删除失败！");
        }
        log.info("设备删除成功 xid:{} status:{}, desc:{}", entity.getNodeXId(), baseReply.getStatus(), baseReply.getDesc());
        if (baseReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.ok();
        } else {
            return DtoResult.error("设备新增失败！", baseReply.getDesc());
        }
    }

    @Resource
    private AlgHall algHall;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Autowired(required = false)
    private SunConfig sunConfig;

    @Override
    public DtoResult<StreamUrlDto> getStreamUrl(GetStreamUrlParamVideoNode param) {
        if (param.getVideoProtocol() == null) {
            param.setVideoProtocol(8);
        }
        if (param.getStreamIndex() == null) {
            param.setStreamIndex(0);
        }
        if (param.getNetworking() == null) {
            param.setNetworking(1);
        }
        if (param.getUserPlatformType() == null) {
            param.setUserPlatformType(1);
        }
        OpenCommonEnum.ValidityPeriodType validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL;
        if (param.getUserPlatformType() == 11) {
            validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG;
        } else if (param.getUserPlatformType() == 12) {
            validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_PERMANENT;
        }
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        if (param.getVideoProtocol() == 9) {
            param.setNetworking(3);
        }
        // 5 webrtc 8 私有流
        if (param.getVideoProtocol() == 8 || param.getVideoProtocol() == 9) {
            if (param.getOpenAi() || param.getStartAi()) {
                //如果开启AI 则返回AI房间
                AlgRoom algRoom = algHall.computeIfAbsent(param.getChannelXId(), (xId) -> {
                    if (StringUtil.isNotEmpty(param.getOpsDeviceChannelEntity().getAiRoom())) {
                        try {
                            OpenStreamMessage.StreamReplyForPublisher streamReplyForPublisher = OpenStreamMessage.StreamReplyForPublisher.parseFrom(Base64.getDecoder().decode(param.getOpsDeviceChannelEntity().getAiRoom()));
                            return new AlgRoom(param.getChannelXId(), streamReplyForPublisher);
                        } catch (Exception e) {
                            log.info("获取AI房间失败！(parseFrom)", e);
                        }
                    }
                    boolean inLocalAreaNetwork = true;
                    if (sunConfig != null) {
                        inLocalAreaNetwork = sunConfig.getConfu().getInLocalAreaNetwork();
                    }
                    log.info("创建新的AI房间！inLocalAreaNetwork:{}", inLocalAreaNetwork);
                    OpenStreamMessage.StreamPublishLowLevelRequest streamPublishRequest = OpenStreamMessage.StreamPublishLowLevelRequest
                            .newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setInLocalAreaNetwork(inLocalAreaNetwork)
                            .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.NETWORK_PROTOCOL_TCP)
                            .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG)
                            .build();
                    OpenStreamMessage.StreamReplyForPublisher streamReplyForPublisher = sunOpenBlockingStub.streamPublishLowLevel(streamPublishRequest);

                    byte[] byteArray = streamReplyForPublisher.toByteArray();
                    opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, param.getOpsDeviceChannelEntity().getId())
                            .set(OpsDeviceChannelEntity::getAiRoom, Base64.getEncoder().encodeToString(byteArray)));
                    return new AlgRoom(param.getChannelXId(), streamReplyForPublisher);
                });
                if (param.getOpenAi()) {
                    return getAiRoom(param, algRoom);
                } else {
                    return startAiRoom(param, algRoom);
                }
            }
            OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                    .newBuilder()
//                    .setPlayTypeValue(param.getPlayType())
                    .setStreamIndex(param.getStreamIndex())
                    .setNetworkingValue(param.getNetworking())
                    .setXId(param.getChannelXId())
                    .setAuthInfo(authInfo)
                    .setValidityPeriodType(validityPeriodType)
                    .build();
            OpenStreamMessage.StreamReplyForPlayer streamReply;
            try {
                streamReply = sunOpenBlockingStub.streamPlayVer1(build);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", streamReply.getDesc());
            }
            byte[] byteArray = streamReply.toByteArray();
            if (param.getVideoProtocol() == 8) {
                streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(byteArray));
            } else {
                streamUrlDto.setSdWebRtcUrl(Base64.getEncoder().encodeToString(byteArray));
            }
        } else if (param.getVideoProtocol() == 4) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_RTSP)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
//                            .setPlayTypeValue(param.getPlayType())
                            .setStreamIndex(param.getStreamIndex())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("开启直播失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setRtsp(standardStreamURIVer.getResult().toStringUtf8());
        } else if (param.getVideoProtocol() == 1) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_HTTP_FLV)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
//                            .setPlayTypeValue(param.getPlayType())
                            .setStreamIndex(param.getStreamIndex())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setHttpFlv(standardStreamURIVer.getResult().toStringUtf8());
        } else if (param.getVideoProtocol() == 7) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_WS_FLV)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
//                            .setPlayTypeValue(param.getPlayType())
                            .setStreamIndex(param.getStreamIndex())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setWsFlv(standardStreamURIVer.getResult().toStringUtf8());
        } else {
            return DtoResult.error("暂不支持！");
        }
        return DtoResult.ok(streamUrlDto);
    }

    /*
     * 1、普通播放
     * StreamPlayVer1
     * 2、AI播放
     * conv-> sun.StreamPublishLowLevel(room) - 创建一个空房间
     * conv-> sun.StreamPlayVer1(playVer1) 拿到原流
     * conv-> room 的 stream_token 调用 sun.StreamJoin(streamJoin1) 拿到confu需要推流的信息
     * confu-> playVer1 的 addr 和 handshake_data 进行帧交互
     * confu-> streamJoin1 的 addr 和 handshake_data 进行推流
     * conv-> room 的 stream_token 调用 sun.StreamJoin(streamJoin2) 拿到观看者的握手信息
     *
     */
    public DtoResult<StreamUrlDto> getAiRoom(GetStreamUrlParamVideoNode param, AlgRoom algRoom) {
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenStreamMessage.StreamJoinRequest streamJoinRequest = OpenStreamMessage.StreamJoinRequest
                .newBuilder()
                .setAuthInfo(grpcConfig.getAuthUser())
                .setByLanAddr(false)
                .setNetworkingValue(param.getNetworking())
                .setStreamToken(algRoom.getStreamReplyForPublisher().getStreamToken())
                .build();
        OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer = sunOpenBlockingStub.streamJoin(streamJoinRequest);
        if (streamReplyForPlayer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取直播流失败！", streamReplyForPlayer.getDesc());
        }
        OpenStreamMessage.StreamReplyForPlayer.Builder builder = streamReplyForPlayer.toBuilder();
        streamReplyForPlayer = builder.build();
        log.info("getAlRoom,xid:{} networking:{}", param.getChannelXId(), param.getNetworking());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        byte[] byteArray = streamReplyForPlayer.toByteArray();
        streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(byteArray));
        return DtoResult.ok(streamUrlDto);
    }


    public DtoResult<StreamUrlDto> startAiRoom(GetStreamUrlParamVideoNode param, AlgRoom algRoom) {
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        byte[] byteArray = algRoom.getStreamReplyForPublisher().toByteArray();
        log.info("startAiRoom 获取AI房间成功！addr{}", algRoom.getStreamReplyForPublisher().getAddr());
        String hex = Hex.encodeHexString(algRoom.getStreamReplyForPublisher().getHandshakeData().toByteArray());
        log.info("startAiRoom 获取AI房间成功！{}", hex);
        streamUrlDto.setSdPushUrl(Base64.getEncoder().encodeToString(byteArray));
        boolean inLocalAreaNetwork = true;
        if (sunConfig != null) {
            inLocalAreaNetwork = sunConfig.getConfu().getInLocalAreaNetwork();
        }
        log.info("startAiRoom 获取AI房间成功！inLocalAreaNetwork:{}", inLocalAreaNetwork);
        OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                .newBuilder()
                .setUseLanAddrForRedirecting(inLocalAreaNetwork)
//                .setPlayTypeValue(param.getPlayType())
                .setStreamIndex(param.getStreamIndex())
                .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.NETWORK_PROTOCOL_TCP)
                .setXId(param.getChannelXId())
                .setAuthInfo(authInfo)
                .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG)
                .build();
        OpenStreamMessage.StreamReplyForPlayer streamReply;
        try {
            streamReply = sunOpenBlockingStub.streamPlayVer1(build);
        } catch (Exception e) {
            log.error("获取直播流失败！", e);
            return DtoResult.error("获取直播流失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取直播流失败！", streamReply.getDesc());
        }
        streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(streamReply.toByteArray()));
        return DtoResult.ok(streamUrlDto);
    }

    @Override
    public DtoResult<Void> stopLive(VideoNodeBaseParam param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        OpenSun.StatefulReply baseReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(param.getChannelXId());
            ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(node);
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
            baseReply = sunOpenBlockingStub.deviceOperation(OpenSunMessage.DeviceOperationRequest.newBuilder()
                    .setAuthInfo(authInfo)
                    .setXId(param.getChannelXId())
                    .setOperation(OpenSunMessage.DeviceOperationRequest.DeviceOperation.DEVICE_OPERATION_STOP_STREAM)
                    .build());
        } catch (Exception e) {
            log.error("停止直播失败！", e);
            return DtoResult.error("停止直播失败！");
        }
        if (baseReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.ok();
        } else {
            return DtoResult.error("停止直播失败！", baseReply.getDesc());
        }
    }

    @Resource
    private SunGrpcUtil sunGrpcUtil;

    @Override
    public DtoResult<VlinkerConvergeSnapshotResp> getSnapshot(GetSnapshotParam param) {
        // 2. 获取设备所在节点
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        VlinkerConvergeSnapshotResp vlinkerConvergeSnapshotResp = null;
        try {
            // 当前时刻
            Instant now = Instant.now();
            if (param.getTimes() == null) {
                param.setTimes(300);
            }
            // 5分钟前
            Instant past = now.minusSeconds(param.getTimes());
            // 转换为 protobuf Timestamp
            Timestamp ts = Timestamp.newBuilder()
                    .setSeconds(past.getEpochSecond())
                    .setNanos(past.getNano())
                    .build();

            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenStreamMessage.GetStreamShotRequest getStreamShotRequest = OpenStreamMessage.GetStreamShotRequest.newBuilder()
                    .setAuthInfo(grpcConfig.getAuthUserByXId(param.getChannelXId()))
                    .setXId(param.getOpsDeviceChannelEntity().getNodeXId())
                    .setJpegImageQuality(90)
                    .setStreamIndex(0)
                    .setMaxLineSize(param.getWidth() == null ? 720 : param.getWidth())
                    .setMustBeNewerThan(ts)
                    .build();
            OpenSun.StatefulReply streamShot = sunOpenBlockingStub.getStreamShot(getStreamShotRequest);
            if (streamShot.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                String base64 = Base64.getEncoder().encodeToString(streamShot.getResult().toByteArray());
                log.info("获取设备快照成功 len:{}", base64.length());
                vlinkerConvergeSnapshotResp = new VlinkerConvergeSnapshotResp();
                vlinkerConvergeSnapshotResp.setType("image/jpeg");
                vlinkerConvergeSnapshotResp.setImg(base64);
                redisUtil.set("CONV:SDK:SNAPSHOT:" + param.getOpsDeviceChannelEntity().getId(), base64, 24, TimeUnit.HOURS);
            } else {
                log.info("没拿到快照 从redis拿一次 status:{} desc:{}", streamShot.getStatus(), streamShot.getDesc());
                Object snapshot = redisUtil.get("CONV:SDK:SNAPSHOT:" + param.getOpsDeviceChannelEntity().getId());
                if (snapshot != null) {
                    vlinkerConvergeSnapshotResp = new VlinkerConvergeSnapshotResp();
                    vlinkerConvergeSnapshotResp.setType("image/jpeg");
                    vlinkerConvergeSnapshotResp.setImg(String.valueOf(snapshot));
                } else {
                    log.error("获取截图失败！ redis也没有");
                    return DtoResult.error("获取截图失败！");
                }
            }
//            ConvergeBaseResp baseResp = sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getOpsDeviceChannelEntity().getNodeXId(),
//                    QxNodeCmdType.CONVERGE_GET_SNAPSHOT.getType());
        } catch (Exception e) {
            log.error("获取截图失败！ 走redis看看？", e);
            Object snapshot = redisUtil.get("CONV:SDK:SNAPSHOT:" + param.getOpsDeviceChannelEntity().getId());
            if (snapshot != null) {
                vlinkerConvergeSnapshotResp = new VlinkerConvergeSnapshotResp();
                vlinkerConvergeSnapshotResp.setType("image/jpeg");
                vlinkerConvergeSnapshotResp.setImg(String.valueOf(snapshot));
            } else {
                log.error("获取截图失败！ redis也没有");
                return DtoResult.error("获取截图失败！");
            }

        }
        return DtoResult.ok(vlinkerConvergeSnapshotResp);
    }

    @Override
    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonthsByCloud(GetChannelRecordMonthsParamVideoNode param) {
        //查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
        DateTime parse = DateUtil.parse(param.getDates() + "01", DatePattern.PURE_DATE_PATTERN);
        String format = DateUtil.format(parse, "yyyy/MM");
        String formatByRes = DateUtil.format(parse, "yyyy-MM");
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.beginOfMonth(parse), DateUtil.endOfMonth(parse), DateField.DAY_OF_MONTH);
        dateTimes.forEach(date -> linkedHashMap.put(date.toString("yyyy-MM-dd"), 0));
        String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode() + "/" + param.getChannelXId() + "/" + format + "/";
        S3ObjectDto s3ObjectDto = ossUtil.listObjects(build, "/", prefix, null);
        s3ObjectDto.getContents().stream().filter(e -> e.getType() == 1).forEach(e -> {
            linkedHashMap.put(formatByRes + "-" + e.getName(), 1);
        });
        return DtoResult.ok(linkedHashMap);
    }


    /**
     * 语音通话
     */
    @Override
    public DtoResult<VlinkerConvergeRtcConnectResp> getVoiceUrl(GetVoiceUrlParam param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        if (param.getPlayType() == null) {
            return DtoResult.error("播放类型为空");
        }

        long count = opsDeviceAvCallService.count(new LambdaQueryWrapper<OpsDeviceAvCallInfoEntity>()
                .eq(OpsDeviceAvCallInfoEntity::getChannelId, param.getDeviceEntity().getId()));
        if (count > 0) {
            log.info("设备{}->{}已有通话中 count:{}", param.getDeviceEntity().getId(), param.getOpsDeviceChannelEntity().getId(), count);
            return DtoResult.error("设备正在通话中！");
        }
        OpenCommonEnum.BaseNetworkingProtocol networkingProtocol;
        try {
            networkingProtocol = OpenCommonEnum.BaseNetworkingProtocol.forNumber(param.getPlayType());
        } catch (Exception e) {
            throw new BizRuntimeException("播放类型错误！");
        }
        OpenStreamMessage.StreamReplyForPublisher streamReply;
        try {
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUser();
            OpenStreamMessage.StreamPublishLowLevelRequest build = OpenStreamMessage.StreamPublishLowLevelRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setNetworking(networkingProtocol)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .build();
            streamReply = sunOpenBlockingStub.streamPublishLowLevel(build);
        } catch (Exception e) {
            log.error("开启对讲失败！", e);
            return DtoResult.error("开启对讲失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            log.error("开启对讲失败：status:{} desc:{}", streamReply.getStatus(), streamReply.getDesc());
            return DtoResult.error("开启对讲失败！", streamReply.getDesc());
        }
        OpenSun.StatefulReply statefulReply;
        try {
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(param.getChannelXId());
            OpenStreamMessage.InviteDeviceToJoinStreamRequest build = OpenStreamMessage.InviteDeviceToJoinStreamRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setStreamToken(streamReply.getStreamToken())
                    .setXid(param.getChannelXId())
                    .build();
            statefulReply = sunOpenBlockingStub.inviteDeviceToJoin(build);
            if (statefulReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                log.error("开启对讲失败：status:{} desc:{}", statefulReply.getStatus(), statefulReply.getDesc());
                return DtoResult.error("开启对讲失败！", statefulReply.getDesc());
            }
        } catch (Exception e) {
            log.error("开启对讲失败！", e);
            return DtoResult.error("开启对讲失败！");
        }
        // 构建并返回GB类型的RTC连接响应
        return DtoResult.ok(VlinkerConvergeRtcConnectResp.builder()
                .type("sd")
                .sdUrl(Base64.getEncoder().encodeToString(streamReply.toByteArray()))
                .sessionId(statefulReply.getResult().toStringUtf8())
                .build());
    }


    public List<S3ObjectDto.ObjectDto> listAllObjects(OSSBean build, String delimiter, String prefix) {
        List<S3ObjectDto.ObjectDto> allContents = new ArrayList<>();
        String continuationToken = null;

        do {
            // 构建查询
            S3ObjectDto s3ObjectDto = ossUtil.listObjects(build, delimiter, prefix, continuationToken);

            if (s3ObjectDto != null && s3ObjectDto.getContents() != null) {
                allContents.addAll(s3ObjectDto.getContents());
            }

            // 获取下一页的 token
            continuationToken = s3ObjectDto != null ? s3ObjectDto.getNextContinuationToken() : null;

        } while (continuationToken != null); // 直到没有 token

        return allContents;
    }


    @Override
    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLineByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        List<ChannelRecordTimeLine> resList = new ArrayList<>();
        // CLOUD: 云存录像; LOCAL: 卡存录像
        //直接查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        List<Date> dates = new ArrayList<>();

        // 将时间戳转换为 LocalDate
        LocalDate startDate = Instant.ofEpochMilli(param.getStart()).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(param.getEnd()).atZone(ZoneId.systemDefault()).toLocalDate();
        // 循环遍历，逐日添加日期
        while (!startDate.isAfter(endDate)) {
            // 将 LocalDate 转换回 Date
            Date date = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            dates.add(date);
            startDate = startDate.plusDays(1); // 加一天
        }
        if (dates.isEmpty()) {
            return DtoResult.ok(resList);
        }
        List<S3ObjectDto.ObjectDto> indexUrl = new ArrayList<>();
        dates.forEach(date -> {
            String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode()
                    + "/" + param.getChannelXId() + "/" + DateUtil.format(date, "yyyy/MM/dd") + "/index/";
            List<S3ObjectDto.ObjectDto> objectDtos = listAllObjects(build, "/", prefix);
            objectDtos.stream()
                    .filter(Objects::nonNull)
                    .filter(e -> e.getName().endsWith(".index"))
                    .filter(e -> e.getType() == 2).forEach(indexUrl::add);
        });
        // parse index files and build timeline
        IndexFileParser parser = new IndexFileParser();
        List<MediaIndexerDataBufferInfo> allBuffers = new ArrayList<>();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        for (S3ObjectDto.ObjectDto objectDto : indexUrl) {
            try {
                String s = ossUtil.downloadUrl(build, objectDto.getKey());
                byte[] bytes = fileService.downloadFileAsByteArrayByCount(s, 3);
                if (bytes == null || bytes.length == 0) {
                    continue;
                }
                baos.write(bytes);
            } catch (Exception e) {
                log.warn("解析index文件失败 key:{}", objectDto.getKey(), e);
            }
        }
        byte[] merged = baos.toByteArray();
        if (merged.length > 0) {
            IndexFileParser.ParseResult parseResult = parser.parse(merged);
            if (parseResult != null && parseResult.buffers != null) {
                allBuffers.addAll(parseResult.buffers);
            }
        }

        // window by requested start/end (ms)
        Long windowStart = param.getStart();
        Long windowEnd = param.getEnd();
        Integer accessWay = param.getDeviceEntity().getAccessWay();
        resList = TimelineBuilder.build(allBuffers, accessWay, windowStart, windowEnd);

        return DtoResult.ok(resList);
    }


    @Override
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        ChannelRecordUrlResp resp = new ChannelRecordUrlResp();
        resp.setAccessWay(param.getDeviceEntity().getAccessWay());
        // CLOUD: 云存录像; LOCAL: 卡存录像
        //直接查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        List<Date> dates = new ArrayList<>();
        // 将时间戳转换为 LocalDate
        LocalDate startDate = Instant.ofEpochMilli(param.getStart()).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(param.getEnd()).atZone(ZoneId.systemDefault()).toLocalDate();
        // 循环遍历，逐日添加日期
        while (!startDate.isAfter(endDate)) {
            // 将 LocalDate 转换回 Date
            Date date = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            dates.add(date);
            startDate = startDate.plusDays(1); // 加一天
        }
        if (dates.isEmpty()) {
            log.error("无录像 start:{},end:{}", param.getStart(), param.getEnd());
            return DtoResult.error("无录像", "没有录像文件");
        }
        // 收集所有 sdmeta 文件信息（按天遍历），按文件名中的最后时间戳过滤与排序
        List<S3ObjectDto.ObjectDto> indexFiles = new ArrayList<>();
        dates.forEach(date -> {
            String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode()
                    + "/" + param.getChannelXId() + "/" + DateUtil.format(date, "yyyy/MM/dd") + "/index/";
            List<S3ObjectDto.ObjectDto> objectDtos = listAllObjects(build, "/", prefix);
            objectDtos.stream()
                    .filter(o -> o != null && o.getType() == 2)
                    .filter(o -> o.getName() != null && o.getName().endsWith(".index"))
                    .forEach(indexFiles::add);
        });
        // parse index files and build timeline
        IndexFileParser parser = new IndexFileParser();
        List<MediaIndexerDataBufferInfo> allBuffers = new ArrayList<>();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        for (S3ObjectDto.ObjectDto objectDto : indexFiles) {
            try {
                String s = ossUtil.downloadUrl(build, objectDto.getKey());
                byte[] bytes = fileService.downloadFileAsByteArrayByCount(s, 3);
                if (bytes == null || bytes.length == 0) {
                    continue;
                }
                baos.write(bytes);
            } catch (Exception e) {
                log.warn("解析index文件失败 key:{}", objectDto.getKey(), e);
            }
        }
        byte[] merged = baos.toByteArray();
        if (merged.length > 0) {
            IndexFileParser.ParseResult parseResult = parser.parse(merged);
            if (parseResult != null && parseResult.buffers != null) {
                allBuffers.addAll(parseResult.buffers);
            }
        }
        if (allBuffers.isEmpty()) {
            log.info("所选时间段内没录像,startDate:{},endDate:{},dates:{}", param.getStart(), param.getEnd(), dates);
            return DtoResult.error("无录像", "所选时间段内没录像");
        }
        // Build RecordsFileQueryReply with sdmeta information
        OpenSunMessage.RecordsFileQueryReply.Builder replyBuilder = OpenSunMessage.RecordsFileQueryReply.newBuilder();
        replyBuilder.setIndexData(ByteString.copyFrom(merged));
        Map<Integer, String> psMap = new HashMap<>();

        String sdMetaPrefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode()
                + "/" + param.getChannelXId();
        String sdMetaDayFormat = "/yyyy/MM/dd/";
        String sdMetaTimeFormat = "/HH_mm_ss_";
        // Add all media buffer info to the reply and generate sdmeta URLs
        for (MediaIndexerDataBufferInfo buffer : allBuffers) {
            long beginTimestampInSeconds = buffer.getBeginTimestampInSeconds();
            Date date = new Date(beginTimestampInSeconds * 1000);
            //  16_26_32_1757665592.sdmeta
            // 时_分_秒_时间戳.sdmeta
            String a = sdMetaPrefix + DateUtil.format(date, sdMetaDayFormat) + "sdmeta" + DateUtil.format(date, sdMetaTimeFormat) + buffer.getBeginTimestampInSeconds() + ".sdmeta";
            psMap.put(Math.toIntExact(buffer.getBeginTimestampInSeconds()), ossUtil.downloadUrl(build, a));
        }
        replyBuilder.putAllPsMap(psMap);
        byte[] byteArray = replyBuilder.build().toByteArray();
        resp.setUrl(Base64.getEncoder().encodeToString(byteArray));
        resp.setPlayFun(2);
        return DtoResult.ok(resp);
    }

    @Override
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByLocal(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        if (param.getStreamIndex() == null) {
            param.setStreamIndex(0);
        }
        ChannelRecordUrlResp resp = new ChannelRecordUrlResp();
        resp.setAccessWay(param.getDeviceEntity().getAccessWay());
        // CLOUD: 云存录像; LOCAL: 卡存录像
        if (param.getNetworking() == null) {
            return DtoResult.error("请指定播放方式");
        }
        OpenStreamMessage.StreamReplyForPlayer streamReply;
        try {
            log.info("获取回放流 入参：{}", JSON.toJSONString(param));
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
            OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                    .newBuilder()
                    .setStreamIndex(param.getStreamIndex())
                    .setPlayback(true)
//                    .setPlayType(OpenCommonEnum.PlayType.PLAY_TYPE_PLAYBACK_MAIN)
                    .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.forNumber(param.getNetworking()))
                    .setPlaybackBeginTimestamp(param.getStart())
                    .setPlaybackEndTimestamp(param.getEnd())
                    .setXId(param.getChannelXId())
                    .setAuthInfo(authInfo)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .build();
            streamReply = sunOpenBlockingStub.streamPlayVer1(build);
        } catch (Exception e) {
            log.error("获取回放流失败！", e);
            return DtoResult.error("获取回放流失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取回放流失败！", streamReply.getDesc());
        }
        byte[] byteArray = streamReply.toByteArray();
        resp.setPlayFun(2);
        resp.setUrl(Base64.getEncoder().encodeToString(byteArray));
        return DtoResult.ok(resp);
    }


    @Override
    public DtoResult<Void> controlPlayback(ControlPlaybackParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        String jsonString = JSONObject.toJSONString(ConvergeControlPlaybackReq.builder()
                .startTime(DateUtil.format(new Date(param.getStartTime()), "yyyyMMddHHmmss"))
                .endTime(DateUtil.format(new Date(param.getEndTime()), "yyyyMMddHHmmss"))
                .speed(param.getSpeed())
                .build());
        sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getDeviceEntity().getNodeXId(),
                QxNodeCmdType.CONVERGE_LOCAL_VIDEO_PLAYBACK.getType(), jsonString);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackParamVideoNode param) {
        return DtoResult.error("不支持下载！");
    }


    @Override
    public DtoResult<Void> stopPlayback(VideoNodeBaseParam param, CommonStopPlayBackReq playBackReq) {
        return DtoResult.ok();
    }

    @Resource
    private OpsDeviceScreenInfoService opsDeviceScreenInfoService;
    @Resource
    private OpsDeviceAvCallService opsDeviceAvCallService;

    @Override
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(VideoNodeBaseParam videoNodeBaseParam, CommonCallDeviceByVideoReq req) {
        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        long count = opsDeviceAvCallService.count(new LambdaQueryWrapper<OpsDeviceAvCallInfoEntity>()
                .eq(OpsDeviceAvCallInfoEntity::getChannelId, videoNodeBaseParam.getDeviceEntity().getId()));
        if (count > 0) {
            log.info("设备{}->{}已有通话中 count:{}", videoNodeBaseParam.getDeviceEntity().getId(), videoNodeBaseParam.getOpsDeviceChannelEntity().getId(), count);
            return DtoResult.error("设备正在通话中！");
        }

        OpsDeviceScreenInfoEntity opsDeviceScreenInfoEntity = opsDeviceScreenInfoService.getById(videoNodeBaseParam.getDeviceEntity().getId());
        if (opsDeviceScreenInfoEntity == null) {
            log.info("设备{}->{}没有屏幕信息", videoNodeBaseParam.getDeviceEntity().getId(), videoNodeBaseParam.getDeviceEntity().getDeviceCode());
            // INSERT INTO `video_converge_system`.`ops_device_screen_info` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `screen_size`, `ui_width`, `ui_height`, `ui_dpi`, `ui_support_touch_flag`, `ui_screen_brightness`, `ui_video_enc_ability`, `ui_max_bit_rate`, `off_screen_time`, `remark`, `ui_gop`, `ui_fps`) 】
            // VALUES (1943576361427902465, NULL, '2025-07-11 16:14:52', NULL, '2025-07-15 12:17:24', 2.8, 240, 320, 100, 0, 60, 2, 100, 30, NULL, 10, 10);
            // 临时创建
//            opsDeviceScreenInfoEntity = new OpsDeviceScreenInfoEntity();
//            opsDeviceScreenInfoEntity.setScreenSize(2.8);
//            opsDeviceScreenInfoEntity.setUiWidth(240);
//            opsDeviceScreenInfoEntity.setUiHeight(320);
//            opsDeviceScreenInfoEntity.setUiDpi(100);
//            opsDeviceScreenInfoEntity.setUiSupportTouchFlag(0);
//            opsDeviceScreenInfoEntity.setUiScreenBrightness(60);
//            opsDeviceScreenInfoEntity.setUiVideoEncAbility(2);
//            opsDeviceScreenInfoEntity.setUiMaxBitRate(100);
//            opsDeviceScreenInfoEntity.setOffScreenTime(30);
//            opsDeviceScreenInfoEntity.setUiGop(10);
//            opsDeviceScreenInfoEntity.setUiFps(10.0);
            return DtoResult.error("读取设备支持的视频格式不存在");
        }
        OpenCommonEnum.AudioFormat audioFormat;
        OpenCommonEnum.VideoFormat videoFormat;
        OpenCommonEnum.BaseNetworkingProtocol networkingProtocol = OpenCommonEnum.BaseNetworkingProtocol.forNumber(req.getNetworkingProtocol());
        if (networkingProtocol == null) {
            log.error("播放类型错误！ getNetworkingProtocol:{}", req.getNetworkingProtocol());
            return DtoResult.error("播放类型错误！");
        }
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(videoNodeBaseParam.getDeviceEntity().getNodeId());
        OpenStreamMessage.StreamReplyForPublisher streamReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUser();
            audioFormat = OpenCommonEnum.AudioFormat.AUDIO_FORMAT_G711A;
            if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == 0x02) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_AVC;
            } else if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == 0x04) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_HEVC;
            } else if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == (0x02 | 0x04)) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_AVC;
            } else {
                log.info("读取设备支持的视频格式异常 {}", opsDeviceScreenInfoEntity.getUiVideoEncAbility());
                return DtoResult.error("发起视频通话失败！", "读取设备支持的视频格式异常");
            }
            OpenStreamMessage.StreamPublishLowLevelRequest build = OpenStreamMessage.StreamPublishLowLevelRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setNetworking(networkingProtocol)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .build();
            streamReply = sunOpenBlockingStub.streamPublishLowLevel(build);
            log.info("streamPublishLowLevel：statusValue{} desc:{}", streamReply.getStatusValue(), streamReply.getDesc());
        } catch (Exception e) {
            log.error("创建视频房间失败！", e);
            return DtoResult.error("创建视频房间失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            log.error("创建视频房间失败！ status:{}, desc:{}", streamReply.getStatusValue(), streamReply.getDesc());
            return DtoResult.error("创建视频房间失败！", streamReply.getDesc());
        }
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(videoNodeBaseParam.getChannelXId());
            OpenStreamMessage.InviteDeviceToJoinStreamRequest build = OpenStreamMessage.InviteDeviceToJoinStreamRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setStreamToken(streamReply.getStreamToken())
                    .setXid(videoNodeBaseParam.getChannelXId())
                    .build();
            OpenSun.StatefulReply statefulReply = sunOpenBlockingStub.inviteDeviceToJoin(build);
            log.info("inviteDeviceToJoin：statusValue{} desc:{}", statefulReply.getStatusValue(), statefulReply.getDesc());
            if (statefulReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                log.error("发起视频通话失败！ res:{}，status:{},desc:{}", statefulReply.getResult(), statefulReply.getStatus(), statefulReply.getDesc());
                return DtoResult.error("发起视频通话失败！", statefulReply.getDesc());
            }
        } catch (Exception e) {
            log.error("发起视频通话失败！", e);
            return DtoResult.error("发起视频通话失败！");
        }
        try {
            OpenStreamMessage.StreamJoinRequest streamJoinRequest = OpenStreamMessage.StreamJoinRequest
                    .newBuilder()
                    .setAuthInfo(grpcConfig.getAuthUser())
                    .setStreamToken(streamReply.getStreamToken())
                    .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.NETWORK_PROTOCOL_WEBSOCKET)
                    .build();
            OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer = sunOpenBlockingStub.streamJoin(streamJoinRequest);
            log.info("调试加入房间：base:{}", Base64.getEncoder().encodeToString(streamReplyForPlayer.toByteArray()));
        } catch (Exception e) {
            log.error("调试失败了！ ");
        }
        CommonCallDeviceByVideoResp callDeviceByVideoResp = new CommonCallDeviceByVideoResp();
        byte[] byteArray = streamReply.toByteArray();
        String callData = Base64.getEncoder().encodeToString(byteArray);
        log.info("statusValue :{} addr:{} byteArray :{} callData: {}", streamReply.getStatusValue(), streamReply.getAddr(), byteArray.length, callData);
        callDeviceByVideoResp.setCallData(callData);
        callDeviceByVideoResp.setVideoFormat(videoFormat.getNumber());
        callDeviceByVideoResp.setAudioFormat(audioFormat.getNumber());
        callDeviceByVideoResp.setUiVideoEncAbility(opsDeviceScreenInfoEntity.getUiVideoEncAbility());
        callDeviceByVideoResp.setUiFps(opsDeviceScreenInfoEntity.getUiFps());
        callDeviceByVideoResp.setUiMaxBitRate(opsDeviceScreenInfoEntity.getUiMaxBitRate());
        callDeviceByVideoResp.setUiWidth(opsDeviceScreenInfoEntity.getUiWidth());
        callDeviceByVideoResp.setUiHeight(opsDeviceScreenInfoEntity.getUiHeight());
        callDeviceByVideoResp.setUiDpi(opsDeviceScreenInfoEntity.getUiDpi());
        callDeviceByVideoResp.setUiSupportTouchFlag(opsDeviceScreenInfoEntity.getUiSupportTouchFlag());
        callDeviceByVideoResp.setUiScreenBrightness(opsDeviceScreenInfoEntity.getUiScreenBrightness());
        callDeviceByVideoResp.setOffScreenTime(opsDeviceScreenInfoEntity.getOffScreenTime());
        callDeviceByVideoResp.setUiGop(opsDeviceScreenInfoEntity.getUiGop());
        String handshakeData = Base64.getEncoder().encodeToString(streamReply.getHandshakeData().toByteArray());
        callDeviceByVideoResp.setHandshakeData(handshakeData);

        // 构建并返回GB类型的RTC连接响应
        return DtoResult.ok(callDeviceByVideoResp);
    }

}
