package com.saida.services.system.video.indexer;

import com.saida.services.system.video.algHall.saidaPlayer.Message;
import lombok.Data;

/**
 * Mirror of C struct MEDIA_INDEXER_DATA_BUFFER_INFO.
 * Layout (little-endian):
 * - Message.Header (12 bytes)
 * - uint32_t bitmap (4 bytes)
 * - uint64_t beginTimestampInSeconds (8 bytes)
 * - uint64_t endTimestampInSeconds (8 bytes)
 *
 * In Message.Header, contentLength should be 20 for this structure (body only).
 */
@Data
public class MediaIndexerDataBufferInfo {
    public static final int BODY_SIZE = 20; // bitmap(4) + begin(8) + end(8)

    // bitmap flags
    public static final int MEDIA_BUFFER_INFO_BITMAP_CONTINUOUS = 1 << 0;
    public static final int MEDIA_BUFFER_INFO_BITMAP_UPLOAD_SUCCESS = 1 << 1;

    private final Message.Header header;
    private final long bitmap; // uint32
    private final long beginTimestampInSeconds; // uint64
    private final long endTimestampInSeconds;   // uint64

    public MediaIndexerDataBufferInfo(Message.Header header, long bitmap, long beginTimestampInSeconds, long endTimestampInSeconds) {
        this.header = header;
        this.bitmap = bitmap;
        this.beginTimestampInSeconds = beginTimestampInSeconds;
        this.endTimestampInSeconds = endTimestampInSeconds;
    }

    public Message.Header getHeader() { return header; }
    public long getBitmap() { return bitmap; }
    public long getBeginTimestampInSeconds() { return beginTimestampInSeconds; }
    public long getEndTimestampInSeconds() { return endTimestampInSeconds; }

    public boolean isContinuous() { return (bitmap & MEDIA_BUFFER_INFO_BITMAP_CONTINUOUS) != 0; }
    public boolean isUploadSuccess() { return (bitmap & MEDIA_BUFFER_INFO_BITMAP_UPLOAD_SUCCESS) != 0; }

    public long getBeginMillis() { return beginTimestampInSeconds * 1000L; }
    public long getEndMillis() { return endTimestampInSeconds * 1000L; }
    public long getDurationMillis() { return Math.max(0, getEndMillis() - getBeginMillis()); }
}

