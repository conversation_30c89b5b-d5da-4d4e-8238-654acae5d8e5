package com.saida.services.system.auth.service;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.HomePageUrlConfig;
import com.saida.services.common.config.JwtConfig;
import com.saida.services.common.dto.JumpDto;
import com.saida.services.common.dto.LoginToAdminByUser;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.enumeration.StatusEnum;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.auth.jwt.JwtGenerator;
import com.saida.services.system.pojo.Token;
import com.saida.services.system.sys.entity.*;
import com.saida.services.system.sys.pojo.JwtUser;
import com.saida.services.system.sys.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class AuthService {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private RolePermissionService rolePermissionService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private JwtGenerator jwtGenerator;

    @Autowired
    private ConvSysOrgService convSysOrgService;

    @Autowired
    private RedisUtil redisUtil;


    /**
     * @return Token
     */
    public DtoResult<Token> getTokenByAdmin(Integer pid) {
        UserEntity user = userService.getOne(new LambdaQueryWrapper<UserEntity>()
                .eq(UserEntity::getIsSuper, 2)
                .eq(UserEntity::getPid, pid)
                .last(" limit 1 "));
        if (user == null) {
            return DtoResult.error("系统管理员不存在，请先添加管理员");
        }
        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            return DtoResult.error("账号已被禁用");
        }
        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return DtoResult.ok(createAccessToken(user));
    }

    @Resource
    private HomePageUrlConfig homePageUrlConfig;


    public DtoResult<JumpDto> loginToAdminByUser(LoginToAdminByUser loginToAdminByUser) {

        String jumpUrl = homePageUrlConfig.getJumpUrl(loginToAdminByUser.getPid());
        if (jumpUrl == null) {
            return DtoResult.error("服务未启用");
        }
        log.info("loginToAdminByUser:{}", loginToAdminByUser);
        if (loginToAdminByUser.getUserId() == null || loginToAdminByUser.getUserId() <= 0
                || loginToAdminByUser.getUserName() == null || loginToAdminByUser.getAccount() == null
                || loginToAdminByUser.getPassword() == null) {
            return DtoResult.error("参数错误", "你怎么能少给我东西呢");
        }
        Long adminRoleId = 100000L + loginToAdminByUser.getPid();
        RoleEntity adminRole = roleService.getById(adminRoleId);

        Long orgId = 1L;
        if (loginToAdminByUser.getPid() == 1) {
            ConvSysOrgEntity one = convSysOrgService.getOne(new LambdaQueryWrapper<ConvSysOrgEntity>()
                    .eq(ConvSysOrgEntity::getParentId, 0));
            if (one != null) {
                orgId = one.getId();
            }
        }
        if (adminRole == null) {
            // 创建一个默认的
            adminRole = new RoleEntity();
            adminRole.setId(adminRoleId);
            adminRole.setName("系统默认管理员");
            adminRole.setOrgId(orgId);
            adminRole.setStatus(1);
            adminRole.setRemark("系统默认管理员");
            adminRole.setCreateUser(loginToAdminByUser.getUserId());
            adminRole.setCreateTime(new Date());
            adminRole.setPid(loginToAdminByUser.getPid());
            roleService.save(adminRole);

            List<PermissionEntity> list = permissionService.list(new LambdaQueryWrapper<PermissionEntity>()
                    .eq(PermissionEntity::getPid, loginToAdminByUser.getPid()));
            List<RolePermissionEntity> rolePermissionEntityList = new ArrayList<>();
            RoleEntity finalAdminRole = adminRole;
            list.forEach(e -> {
                RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
                rolePermissionEntity.setRid(finalAdminRole.getId());
                rolePermissionEntity.setPid(e.getId());
                rolePermissionEntity.setCreateUser(loginToAdminByUser.getUserId());
                rolePermissionEntity.setCreateTime(new Date());
                rolePermissionEntityList.add(rolePermissionEntity);
            });
            rolePermissionService.saveBatch(rolePermissionEntityList);
        }

        UserEntity user = userService.getOne(new LambdaQueryWrapper<UserEntity>()
                .eq(UserEntity::getYyId, loginToAdminByUser.getUserId())
                .eq(UserEntity::getPid, loginToAdminByUser.getPid()));
        if (user == null) {
            user = new UserEntity();
            String account = loginToAdminByUser.getAccount();
            // 这个 account 可能会重复
            int i = 1;
            while (true) {
                if (i > 100) {
                    return DtoResult.error("登录失败", "登录失败，我实在没办法给你分配一个可以使用的账号");
                }
                if (userService.count(new LambdaQueryWrapper<UserEntity>()
                        .eq(UserEntity::getAccount, account + i)
                        .eq(UserEntity::getPid, loginToAdminByUser.getPid())) == 0) {
                    break;
                }
                i++;
            }
            user.setAccount(account + i);
            user.setName(loginToAdminByUser.getUserName());
            user.setPassword(loginToAdminByUser.getPassword());
            user.setOrgId(orgId);
            user.setPid(loginToAdminByUser.getPid());
            user.setYyId(loginToAdminByUser.getUserId());
            userService.save(user);
        } else {
            if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
                return DtoResult.error("账号已被禁用");
            }
        }

        UserRoleEntity userRoleEntity = userRoleService.getOne(new LambdaQueryWrapper<UserRoleEntity>()
                .eq(UserRoleEntity::getUid, user.getId())
                .eq(UserRoleEntity::getRid, adminRole.getId()));
        if (userRoleEntity == null) {
            userRoleEntity = new UserRoleEntity();
            userRoleEntity.setUid(user.getId());
            userRoleEntity.setRid(adminRole.getId());
            userRoleEntity.setCreateUser(loginToAdminByUser.getUserId());
            userRoleEntity.setCreateTime(new Date());
            userRoleService.save(userRoleEntity);
        }
//        return DtoResult.ok(createAccessToken(user));
        // 为什么要这么做呢 因为有些参数是db的默认值 如果不回查的话 user有些字段是空的 会导致jwt生成的数据也是错误的
        UserEntity userEntity = userService.getById(user.getId());
        if (userEntity == null) {
            log.error("用户不存在:{}", user.getId());
            return DtoResult.error("用户不存在", "很离奇的问题");
        }
        userEntity.setPassword(null);
        userEntity.setPageNum(null);
        userEntity.setPageSize(null);

        Token accessToken = createAccessToken(userEntity);
        JumpDto build = JumpDto.builder()
                .type(loginToAdminByUser.getPid())
                .url(jumpUrl)
                .token(accessToken)
                .jumpUrl(jumpUrl + "?token=" + accessToken.getAccessToken() + "&unsecret=1")
                .build();
        log.info("loginToAdminByUser JumpDto:{}", build);
        return DtoResult.ok(build);

    }

    /**
     * 根据用户名密码获取token
     * @param username 用户名
     * @param password 加密后的密码
     * @return Token
     */
    public Token getTokenByUserNamePassword(String username, String password, Integer pid) {
        if (StringUtil.isEmpty(username) || StringUtil.isEmpty(password)) {
            throw new BizRuntimeException("账号密码必填");
        }
        UserEntity user = checkLoginByUserNamePassword(username, password, pid);

        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            throw new BizRuntimeException("账号已被禁用");
        }

        user = userService.getInfo(user.getId());
        if (SDNumberUtil.equals(user.getFreeze(), 1)) {
            throw new BizRuntimeException("您的账户已到期，请尽快续费！");
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        Object recordLog = request.getAttribute("log_record");
        if (recordLog != null) {
            OperatorLogEntity logEntity = (OperatorLogEntity) recordLog;
            logEntity.setAccount(user.getAccount());
            logEntity.setOrgId(user.getOrgId());
            logEntity.setUserName(user.getName());
            logEntity.setUserId(user.getId());
            request.setAttribute("log_record", logEntity);
        }
        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return createAccessToken(user);
    }


    /**
     * 根据手机号获取token
     * @param phone 手机号
     * @return Token
     */
    public Token getTokenByPhone(String phone, Integer pid) {
        if (StringUtil.isEmpty(phone)) {
            throw new BizRuntimeException("手机号必填");
        }
        UserEntity user = userService.getByAccountOrPhone(phone, pid);
        if (user == null) {
            throw new BizRuntimeException("账号不存在");
        }

        user = userService.getInfo(user.getId());
        if (!SDNumberUtil.equals(StatusEnum.ONE.getCode(), user.getStatus())) {
            throw new BizRuntimeException("账号已被禁用");
        }

        user = userService.getInfo(user.getId());
        if (SDNumberUtil.equals(user.getFreeze(), 1)) {
            throw new BizRuntimeException("您的账户已到期，请尽快续费！");
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        Object recordLog = request.getAttribute("log_record");
        if (recordLog != null) {
            OperatorLogEntity logEntity = (OperatorLogEntity) recordLog;
            logEntity.setAccount(user.getAccount());
            logEntity.setOrgId(user.getOrgId());
            logEntity.setUserName(user.getName());
            logEntity.setUserId(user.getId());
            request.setAttribute("log_record", logEntity);
        }
        user.setPassword(null);
        user.setPageNum(null);
        user.setPageSize(null);
        return createAccessToken(user);
    }

    /**
     * 根据用户名密码获取用户信息
     * @param username 用户名
     * @param password 加密后的密码
     * @return UserEntity
     */
    public UserEntity checkLoginByUserNamePassword(String username, String password, Integer pid) {
        UserEntity user = userService.getByAccountOrPhone(username, pid);
        if (user == null) {
            throw new BizRuntimeException("账号或密码错误");
        }
        String key = "CONV:LOGIN:FAIL:" + username;
        Object loginFail = redisUtil.get(key);
        int loginFailCount = 0;
        if (loginFail != null) {
            loginFailCount = Integer.parseInt(loginFail.toString());
        }
        if (loginFailCount >= 5) {
            throw new BizRuntimeException("账户已被锁定，请15分钟后再试！");
        }
        if (BCrypt.checkpw(password, user.getPassword())) {
            redisUtil.del(key);
            return user;
        }
        loginFailCount = loginFailCount + 1;
        redisUtil.set(key, String.valueOf(loginFailCount), 60 * 15);
        if (loginFailCount >= 5) {
            throw new BizRuntimeException("账户已被锁定，请15分钟后再试！");
        }
        throw new BizRuntimeException("账号或密码错误，" + (5 - loginFailCount) + "次错误后账号锁定15分钟");
    }

    /**
     * 根据用户信息生成访问令牌（AccessToken）和刷新令牌（RefreshToken）。
     * <p>
     * 本方法首先验证用户信息是否为空，然后根据用户信息创建JWT用户对象，并填充用户属性。
     * 接着，根据用户所属组织的信息，进一步完善JWT用户对象的数据。
     * 最后，使用JWT生成器创建访问令牌和刷新令牌，并返回包含这两者的Token对象。
     *
     * @param user 用户实体，包含用户的基本信息和组织信息。
     * @return Token对象，包含访问令牌和刷新令牌。
     * @throws BizRuntimeException 如果用户信息为空，则抛出业务运行时异常。
     */
    public Token createAccessToken(UserEntity user) {
        // 验证用户信息是否为空，如果为空则抛出异常
        if (user == null) {
            throw new BizRuntimeException("用户信息错误");
        }

        // 创建JWT用户对象，并复制用户实体的属性到JWT用户对象
        JwtUser jwtUser = new JwtUser();
        BeanUtils.copyProperties(user, jwtUser);

        // 根据用户所属组织ID，获取组织信息
        ConvSysOrgEntity org = convSysOrgService.getById(user.getOrgId());
        // 如果组织信息存在，则设置组织ID链
        if (org != null) {
            jwtUser.setOrgIdChain(org.getIdChain());
        }

        // 使用JWT生成器创建访问令牌和刷新令牌
        String accessToken = jwtGenerator.createAccessToken(jwtUser, user);
        String refreshToken = jwtGenerator.createRefreshToken(jwtUser);

        // 返回包含访问令牌和刷新令牌的Token对象
        return Token.builder().accessToken(accessToken).accessTokenExpire(JwtConfig.getInstance().getTokenExpire() * 60L).refreshToken(refreshToken).build();
    }


    /**
     * 用户登出功能。
     * 该方法主要用于从Redis中删除用户的JWT令牌，从而实现用户登出功能。
     * 登出过程中，首先通过JWT工具类获取JWT的唯一标识符JTI，然后根据JTI从Redis中删除相应的JWT令牌。
     * 如果JTI不存在，则抛出BizRuntimeException异常，提示认证信息错误。
     */
    public void logout() {
        // 获取JWT的唯一标识符JTI
        String jti = JwtUtil.getJti();
        // 检查JTI是否为空，如果为空则抛出异常，表示认证信息错误
        if (StringUtil.isEmpty(jti)) {
            throw new BizRuntimeException("认证信息错误");
        }
        // 根据JTI从Redis中删除JWT令牌
        redisUtil.del(RedisConstants.USER_JWT + jti);
    }
}
