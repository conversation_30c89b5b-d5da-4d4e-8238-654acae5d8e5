package com.saida.services.system.feign;

import com.google.common.collect.Lists;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.deviceApi.req.*;
import com.saida.services.converge.dto.*;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.ConvDeviceAndChannelDTO;
import com.saida.services.converge.entity.dto.DeviceAndChannelDto;
import com.saida.services.converge.entity.dto.DeviceChannelDto;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.entity.params.OpsDeviceParams;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.converge.enums.AccessWayType;
import com.saida.services.converge.qxNode.req.*;
import com.saida.services.converge.qxNode.req.AddPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.DeletePeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.SearchPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.resp.ChannelRecordTimeLine;
import com.saida.services.converge.qxNode.resp.ChannelRecordUrlResp;
import com.saida.services.converge.qxNode.resp.SearchPeopleByHumanGateResp;
import com.saida.services.converge.qxNode.resp.qx.QxSearchCarInfoByGateResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeSdCardCapacityResp;
import com.saida.services.converge.vo.*;
import com.saida.services.deviceApi.req.CommonGetHumanoidMarkersReq;
import com.saida.services.deviceApi.req.CommonGetSoundAndLightShockReq;
import com.saida.services.deviceApi.resp.*;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.feign.converge.system.IFeignConvergeSystemApiController;
import com.saida.services.open.biz.req.ThirdPartyPlatformsQuerySupportPrecisePtzReq;
import com.saida.services.open.biz.resp.ThirdPartyPlatformsQuerySupportPrecisePtzResp;
import com.saida.services.open.dto.GetDeviceCapacityDto;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.req.*;
import com.saida.services.open.req.rtc.VlinkerRtcConnectReq;
import com.saida.services.open.resp.*;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.api.param.RtcConnParam;
import com.saida.services.system.api.service.ApiDeviceService;
import com.saida.services.system.api.service.ApiOrgService;
import com.saida.services.system.ops.controller.DeviceModelVersionController;
import com.saida.services.system.ops.controller.OpsDeviceAlarmController;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.pb.OpenCommonEnum;
import com.saida.services.system.sys.dto.SysOrgDto;
import com.saida.services.system.sys.service.ConvSysOrgService;
import com.saida.services.system.video.param.GetChannelRecordMonthsParamVideoNode;
import com.saida.services.system.video.param.GetChannelRecordTimeLineParamVideoNode;
import com.saida.services.system.video.param.PlaybackPlanParamVideoNode;
import com.saida.services.system.video.service.VideoCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
public class FeignConvergeSystemApiController implements IFeignConvergeSystemApiController {
    private static final Logger log = LoggerFactory.getLogger(FeignConvergeSystemApiController.class);

    @Resource
    private ApiDeviceService apiDeviceService;
    @Resource
    private ConvSysOrgService convSysOrgService;
    @Resource
    private ApiOrgService apiOrgService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private OpsDeviceAlarmController opsDeviceAlarmController;
    @Resource
    private DeviceModelService deviceModelService;
    @Resource
    private DeviceModelVersionService deviceModelVersionService;
    @Resource
    private DeviceModelVersionController deviceModelVersionController;
    @Resource
    private StorageStrategyService storageStrategyService;
    @Resource
    private VideoCommonService videoCommonService;
    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Resource
    private CloudStorageService cloudStorageService;

    @Override
    @PostMapping("/feign/converge-system/getDeviceTreeList")
    public DtoResult<List<SysOrgDto>> getDeviceTreeList() {
        List<ConvSysOrgEntity> tree = apiOrgService.getTree();
        return DtoResult.ok(BeanUtil.copyToList(tree, SysOrgDto.class));
    }

    /**
     * 根据请求获取设备列表。
     * 该方法通过Feign客户端调用，用于查询设备及其通道的信息。
     * 如果请求中未指定组织ID，则尝试获取默认的根组织，并以此作为查询条件。
     * 如果请求中指定了组织ID，则直接使用该组织ID进行查询。
     * 查询条件还包括设备的SN、是否为子设备以及分页信息。
     *
     * @param req 查询请求对象，包含组织ID、设备SN、是否为子设备、分页信息等。
     * @return 返回设备列表的分页响应对象，包含查询结果及分页信息。
     */
    @Override
    @PostMapping("/feign/converge-system/getDeviceChannelList")
    public DtoResult<BasePageInfoEntity<DeviceAndChannelDto>> getDeviceChannelList(VlinkerConvergeDeviceListReq req) {
        // 初始化设备实体对象
        OpsDeviceParams deviceEntity = new OpsDeviceParams();

        // 判断请求中是否指定了组织ID
        if (req.getOrgId() == null) {
            // 尝试获取默认的根组织
            ConvSysOrgEntity org = convSysOrgService.getOne(new LambdaQueryWrapper<ConvSysOrgEntity>().eq(ConvSysOrgEntity::getParentId, 0L), false);
            // 如果根组织不存在，返回错误信息
            if (org == null) {
                return DtoResult.error("未找到根组织");
            }
            // 设置设备实体的组织ID和组织ID链
            deviceEntity.setOrgId(org.getId());
            deviceEntity.setOrgIdChain(org.getIdChain());
        } else {
            // 根据请求中的组织ID直接获取组织信息
            ConvSysOrgEntity org = convSysOrgService.getById(req.getOrgId());
            // 如果组织不存在，返回错误信息
            if (org == null) {
                return DtoResult.error("未找到根组织");
            }
            // 设置设备实体的组织ID和组织ID链
            deviceEntity.setOrgId(org.getId());
            deviceEntity.setOrgIdChain(org.getIdChain());
        }

        // 根据请求填充设备实体的其他查询条件
        deviceEntity.setName(req.getDeviceName());
        deviceEntity.setDeviceCode(req.getSn());
        deviceEntity.setSubLevel(req.getSub() ? 1 : 0);
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setPageNum(req.getPageNum());
        baseRequest.setPageSize(req.getPageSize());

        // 调用API设备服务查询满足条件的设备及其通道的分页信息
        BasePageInfoEntity<DeviceAndChannelDto> page = apiDeviceService.listPageByOpen(deviceEntity, baseRequest);

        // 返回查询结果的分页响应对象
        return DtoResult.ok(page);
    }

    @Override
    @PostMapping("/feign/converge-system/getDeviceList")
    public DtoResult<BasePageInfoEntity<DeviceAndChannelDto>> getDeviceList(VlinkerConvergeDeviceListReq req) {
        // 初始化设备实体对象
        OpsDeviceParams deviceEntity = new OpsDeviceParams();

        // 判断请求中是否指定了组织ID
        if (req.getOrgId() == null) {
            // 尝试获取默认的根组织
            ConvSysOrgEntity org = convSysOrgService.getOne(new LambdaQueryWrapper<ConvSysOrgEntity>().eq(ConvSysOrgEntity::getParentId, 0L), false);
            // 如果根组织不存在，返回错误信息
            if (org == null) {
                return DtoResult.error("未找到根组织");
            }
            // 设置设备实体的组织ID和组织ID链
            deviceEntity.setOrgId(org.getId());
            deviceEntity.setOrgIdChain(org.getIdChain());
        } else {
            // 根据请求中的组织ID直接获取组织信息
            ConvSysOrgEntity org = convSysOrgService.getById(req.getOrgId());
            // 如果组织不存在，返回错误信息
            if (org == null) {
                return DtoResult.error("未找到根组织");
            }
            // 设置设备实体的组织ID和组织ID链
            deviceEntity.setOrgId(org.getId());
            deviceEntity.setOrgIdChain(org.getIdChain());
        }

        // 根据请求填充设备实体的其他查询条件
        deviceEntity.setDeviceCode(req.getSn());
        deviceEntity.setSubLevel(req.getSub() ? 1 : 0);
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setPageNum(req.getPageNum());
        baseRequest.setPageSize(req.getPageSize());

        // 调用API设备服务查询满足条件的设备及其通道的分页信息
        BasePageInfoEntity<DeviceAndChannelDto> page = apiDeviceService.deviceListPageByOpen(deviceEntity, baseRequest);

        // 返回查询结果的分页响应对象
        return DtoResult.ok(page);
    }


    /**
     * 通过Feign客户端调用API设备服务，获取设备的通道列表。
     * 此方法定义了请求的URL路径和请求方法类型，专门用于从API设备服务中获取通道列表。
     *
     * @param req 请求体，包含需要获取通道列表的设备序列号（SN）。
     * @return 返回一个包含通道列表的DTO结果。如果获取成功，返回包含通道列表的DtoResult；如果获取失败，返回一个错误信息的DtoResult。
     */
    @Override
    @PostMapping("/feign/converge-system/getChannelList")
    public DtoResult<List<DeviceChannelDto>> getChannelList(VlinkerConvergeChannelListReq req) {
        try {
            // 调用API设备服务的getChannelList方法，传入设备序列号，获取通道列表
            List<DeviceChannelDto> channelList = apiDeviceService.getChannelList(req.getSn());
            // 返回获取到的通道列表，操作成功
            return DtoResult.ok(channelList);
        } catch (Exception e) {
            // 记录获取通道列表失败的日志
            log.error("获取通道列表失败", e);
            // 返回获取通道列表失败的错误信息，操作失败
            return DtoResult.error("获取通道列表失败");
        }
    }


    /**
     * 通过Feign客户端调用设备服务接口，获取视频直播URL。
     * <p>
     * 此方法是一个Feign接口的实现，用于远程调用设备服务中的获取视频直播URL的功能。
     * 它接收一个请求对象，返回一个包含直播URL的响应对象。
     *
     * @param req 请求对象，包含获取视频直播URL所需的信息。
     * @return 响应对象，包含获取到的视频直播URL以及其他可能的相关信息。
     */
    @Override
    @PostMapping("/feign/converge-system/getVideoLiveUrl")
    public DtoResult<StreamUrlDto> getVideoLiveUrl(VlinkerConvergeVideoLiveUrlReq req) {
        return apiDeviceService.getLiveUrl(req);
    }

    @Override
    @PostMapping("/feign/converge-system/stopLive")
    public DtoResult<Void> stopLive(VlinkerConvergeVideoLiveUrlReq req) {
        return apiDeviceService.stopLive(req);
    }

    @Override
    @PostMapping("/feign/converge-system/getVirtuallyData")
    public DtoResult<VlinkerConvergeVirtuallyDataResp> getVirtuallyData(VlinkerConvergeVirtuallyDataReq req) {
        return apiDeviceService.getVirtuallyData(req);
    }

    /**
     * 控制摄像头的PTZ（Pan/Tilt/Zoom）功能。
     * <p>
     * 该方法通过调用apiDeviceService的ptzCmd方法，实现对摄像头的PTZ控制。PTZ控制包括对摄像头的移动（pan和tilt）
     * 和变焦（zoom）操作。此方法接收一个VlinkerConvergePtzControlReq对象作为参数，该对象包含了进行PTZ控制所需的具体指令。
     * 方法返回一个DtoResult<Void>对象，其中DtoResult是用于封装操作结果的通用数据传输对象，Void表示此操作没有返回值。
     *
     * @param req 包含PTZ控制指令的请求对象。
     * @return 包含操作结果的DtoResult对象，此操作没有具体返回值，因此Void类型。
     */
    @Override
    @PostMapping("/feign/converge-system/ptzControl")
    public DtoResult<Void> ptzControl(VlinkerConvergePtzControlReq req) {
        return apiDeviceService.ptzCmd(req);
    }

    @Override
    @PostMapping("/feign/converge-system/getPtz")
    public DtoResult<VlinkerConvergeGetPtzResp> getPtz(VlinkerConvergeGetPtzReq req) {
        return apiDeviceService.getPtz(req);
    }

    @Override
    @PostMapping("/feign/converge-system/setPtz")
    public DtoResult<Void> setPtz(VlinkerConvergeSetPtzReq req) {
        return apiDeviceService.setPtz(req);
    }


    @Override
    @PostMapping("/feign/converge-system/getFillLightStatus")
    public DtoResult<CommonSetStatusLightResp> getFillLightStatus(VlinkerConvergeFillLightStatusReq req) {
        return apiDeviceService.getFillLightStatus(req);
    }

    @Override
    @PostMapping("/feign/converge-system/setFillLightStatus")
    public DtoResult<VlinkerConvergeFillLightStatusResp> setFillLightStatus(VlinkerConvergeFillLightStatusReq req) {
        return apiDeviceService.setFillLightStatus(req);
    }

    @Override
    @PostMapping("/feign/converge-system/getHomePosition")
    public DtoResult<VlinkerConvergeHomePositionResp> getHomePosition(VlinkerConvergeHomePositionReq req) {
        return apiDeviceService.getHomePosition(req);
    }

    @Override
    @PostMapping("/feign/converge-system/setHomePosition")
    public DtoResult<VlinkerConvergeHomePositionResp> setHomePosition(VlinkerConvergeHomePositionReq req) {
        return apiDeviceService.setHomePosition(req);
    }

    @Override
    public DtoResult<CommonGetRecordMonthResp> getRecordMonth(ConvGetRecordMonthReq req) {
        GetChannelRecordMonthsParamVideoNode videoNode = new GetChannelRecordMonthsParamVideoNode();
        videoNode.setDeviceCode(req.getDeviceCode());
        videoNode.setChannelId(req.getChannelId());
        videoNode.setDeviceId(req.getDeviceId());
        videoNode.setSource(req.getSource());
        videoNode.setDates(req.getMonth());
        DtoResult<LinkedHashMap<String, Integer>> channelRecordMonths = apiDeviceService.getChannelRecordMonths(videoNode);
        if (!channelRecordMonths.success()) {
            return DtoResult.error(channelRecordMonths.getMessage(), channelRecordMonths.getError());
        }
        CommonGetRecordMonthResp resp = new CommonGetRecordMonthResp();
        resp.setDateList(channelRecordMonths.getData());
        return DtoResult.ok(resp);
    }

    /**
     * 通过Feign调用视频服务，获取视频回看URL。
     *
     * @param req 包含设备SN、通道ID、开始时间、结束时间等信息的请求对象。
     * @return 返回包含视频回看URL及相关信息的响应对象，如果设备不存在或获取URL失败，则返回错误信息。
     */
    @Override
    @PostMapping("/feign/converge-system/getVideoBackUrl")
    public DtoResult<VlinkerConvergeVideoBackUrlResp> getVideoBackUrl(VlinkerVideoBackReq req) {
        // 根据设备SN查询设备信息。
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, req.getDeviceSN())
                // .eq(DeviceEntity::getChannelId, req.getChannelId())
                .last("LIMIT 1"), false
        );
        // 检查设备是否存在。
        if (null == deviceEntity) {
            return DtoResult.error("设备不存在！");
        }
        // 从请求中获取SSRC。
        GetChannelRecordTimeLineParamVideoNode baseRequest = new GetChannelRecordTimeLineParamVideoNode();
        baseRequest.setDeviceId(deviceEntity.getId());
        baseRequest.setChannelId(req.getChannelId());
        baseRequest.setStart(req.getStart());
        baseRequest.setEnd(req.getEnd());
        baseRequest.setSource(req.getSource());
        baseRequest.setUuid(req.getUuid());
        baseRequest.setNetworking(req.getNetworking());
        // 调用视频服务，获取视频回看URL。
        DtoResult<ChannelRecordUrlResp> dtoResult = apiDeviceService.getChannelRecordUrl(baseRequest);
        // 检查是否成功获取URL。
        if (dtoResult.success()) {
            // 构建并返回包含视频回看URL及相关信息的响应对象。
            VlinkerConvergeVideoBackUrlResp vlinkerConvergeVideoBackUrlResp = new VlinkerConvergeVideoBackUrlResp();
            vlinkerConvergeVideoBackUrlResp.setUrl(dtoResult.getData().getUrl());
            vlinkerConvergeVideoBackUrlResp.setPlayFun(dtoResult.getData().getPlayFun());
            vlinkerConvergeVideoBackUrlResp.setDownloadUrl(dtoResult.getData().getDownload_url());
            vlinkerConvergeVideoBackUrlResp.setAccessWay(dtoResult.getData().getAccessWay());
            return DtoResult.ok(vlinkerConvergeVideoBackUrlResp);
        }
        // 如果获取URL失败，返回相应的错误信息。
        return DtoResult.error(dtoResult.getMessage(), dtoResult.getError());
    }


    /**
     * 停止播放操作。
     * <p>
     * 此方法用于根据设备序列号和频道ID停止设备的播放。它首先尝试根据提供的设备序列号查找设备实体，
     * 如果设备存在，则构造一个停止播放的DTO并调用视频服务来停止播放。如果停止播放操作成功，
     * 则返回一个成功的DTO结果；否则，返回一个包含错误消息的DTO结果。
     *
     * @param req 停止播放请求对象，包含设备序列号、频道ID和UUID。
     * @return DtoResult<Void> 停止播放操作的结果，可能包含错误消息。
     */
    @Override
    public DtoResult<Void> stopPlayback(VlinkerStopPlaybackReq req) {
        // 根据设备序列号查询设备实体
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                // .eq(DeviceEntity::getChannelId, req.getChannelId())
                .last("LIMIT 1"), false
        );
        // 检查设备是否存在
        if (null == deviceEntity) {
            return DtoResult.error("设备不存在！");
        }
        // 构造停止播放DTO
        String ssrc = req.getUuid();
        StopPlaybackDto dto = new StopPlaybackDto(deviceEntity.getId(), deviceEntity.getDeviceCode(), req.getChannelId(), ssrc);
        // 调用视频服务停止播放
        DtoResult<Void> dtoResult = apiDeviceService.stopPlayback(dto);
        // 根据视频服务的返回结果构造最终的DTO结果
        if (dtoResult.success()) {
            return DtoResult.ok();
        }
        return DtoResult.error(dtoResult.getMessage());
    }

    /**
     * 控制播放接口。
     * 通过传入的播放控制请求，查找相应的设备并调用视频服务进行播放控制操作。
     *
     * @param req 播放控制请求对象，包含设备SN、通道ID、UUID和缩放比例等信息。
     * @return 返回播放控制操作的结果。
     */
    @Override
    public DtoResult<GbControlLocalPlaybackVo> controlPlayback(VlinkerControlPlaybackReq req) {
        // 根据设备SN查询设备信息，限制查询结果为1条。
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                // .eq(DeviceEntity::getChannelId, req.getChannelId())
                .last("LIMIT 1"), false
        );

        // 检查设备是否存在，如果不存在则返回错误信息。
        if (null == deviceEntity) {
            return DtoResult.error("设备不存在！");
        }

        // 创建播放控制DTO，包含设备ID、通道ID、UUID和缩放比例等信息。
        ControlPlaybackDto dto = new ControlPlaybackDto(deviceEntity.getId(), req.getChannelId(), req.getUuid(), req.getScale());

        // 调用视频服务进行播放控制操作，并返回操作结果。
        return apiDeviceService.controlPlayback(dto);
    }

    /**
     * 根据请求下载播放记录。
     *
     * @param req 下载播放记录的请求对象，包含设备SN、通道ID、UUID、开始时间和结束时间等信息。
     * @return 返回下载播放记录的结果，包含下载的播放记录信息。
     * @throws BizRuntimeException 如果设备不存在，则抛出异常。
     */
    @Override
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(VlinkerDownloadPlaybackReq req) {
        // 根据设备SN查询设备信息，限制查询结果为1条。
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                // .eq(DeviceEntity::getChannelId, req.getChannelId())
                .last("LIMIT 1"), false
        );

        // 检查设备是否存在，如果不存在则返回错误信息。
        if (null == deviceEntity) {
            return DtoResult.error("设备不存在！");
        }

        // 创建下载播放记录的DTO，包含设备ID、通道ID、UUID、开始时间和结束时间等信息。
        DownloadPlaybackDto dto = new DownloadPlaybackDto(deviceEntity.getId(), req.getChannelId(), req.getUuid(), req.getStart(), req.getEnd());

        // 调用视频服务下载播放记录，并返回下载结果。
        return apiDeviceService.downloadPlayback(dto);
    }

    /**
     * 停止下载回放接口。
     *
     * @param req 请求对象，包含停止下载回放所需的信息。
     * @return 返回操作结果，包含是否成功停止下载回放的信息。
     */
    @Override
    public DtoResult<GbStopDownloadLocalPlaybackVo> stopDownloadPlayback(VlinkerStopDownloadPlaybackReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                // .eq(DeviceEntity::getChannelId, req.getChannelId())
                .last("LIMIT 1"), false
        );

        // 检查设备是否存在
        if (null == deviceEntity) {
            return DtoResult.error("设备不存在！");
        }

        // 构造下载回放信息DTO
        DownloadPlaybackDto dto = new DownloadPlaybackDto(deviceEntity.getId(), req.getChannelId(), req.getUuid(), req.getStart(), req.getEnd());

        // 调用视频服务停止下载回放
        return apiDeviceService.stopDownloadPlayback(dto);
    }

    @Override
    @PostMapping("/feign/converge-system/getRecordTimeLine")
    public DtoResult<List<VlinkerConvergeRecordTimeLineResp>> getRecordTimeLine(VlinkerRecordTimeLineReq req) {
        GetChannelRecordTimeLineParamVideoNode videoNode = new GetChannelRecordTimeLineParamVideoNode();
        videoNode.setDeviceCode(req.getDeviceCode());
        videoNode.setChannelId(req.getChannelId());
        videoNode.setStart(req.getStart());
        videoNode.setEnd(req.getEnd());
        videoNode.setSource(req.getSource());
        videoNode.setSsrc(req.getSsrc());

        DtoResult<List<ChannelRecordTimeLine>> channelRecordTimeLine = apiDeviceService.getChannelRecordTimeLine(videoNode);
        if (!channelRecordTimeLine.success()) {
            return DtoResult.error(channelRecordTimeLine.getMessage(), channelRecordTimeLine.getError());
        }
        List<VlinkerConvergeRecordTimeLineResp> recordTimeLineRespList = channelRecordTimeLine.getData()
                .stream().map(data -> {
                    VlinkerConvergeRecordTimeLineResp resp = new VlinkerConvergeRecordTimeLineResp();
                    resp.setAccessWay(data.getAccessWay());
                    resp.setDuration(data.getDuration());
//                    resp.setMediaId(data.getMediaId());
                    resp.setStart(data.getStart());
                    resp.setMediaId(data.getMedia_id());
                    return resp;
                }).collect(Collectors.toList());
        return DtoResult.ok(recordTimeLineRespList);
    }

    @Override
    @PostMapping("/feign/converge-system/getPreSetList")
    public DtoResult<List<VlinkerConvergePreSetListResp>> getPreSetList(VlinkerPreSetListReq req) {
        return apiDeviceService.preSetList(req);
    }

    @Override
    @PostMapping("/feign/converge-system/setPreSet")
    public DtoResult<Void> setPreSet(VlinkerSetPreSetReq req) {
        return apiDeviceService.preSet(req);
    }

    @Override
    @PostMapping("/feign/converge-system/jumpPreSet")
    public DtoResult<Void> jumpPreSet(VlinkerJumpPreSetReq req) {
        return apiDeviceService.preSetJump(req);
    }

    @Override
    @PostMapping("/feign/converge-system/delPreSet")
    public DtoResult<Void> delPreSet(VlinkerDelPreSetReq req) {
        return apiDeviceService.preSetDelete(req);
    }

    @Override
    @PostMapping("/feign/converge-system/rtcConnect")
    public DtoResult<VlinkerConvergeRtcConnectResp> rtcConnect(VlinkerRtcConnectReq req) {
        RtcConnParam rtcConnParam = new RtcConnParam();
        rtcConnParam.setDeviceCode(req.getDeviceCode());
        rtcConnParam.setChannelId(req.getChannelId());
        rtcConnParam.setPlayType(req.getPlayType());
        return apiDeviceService.rtcConn(rtcConnParam);
    }


    @Override
    public DtoResult<VlinkerConvergeFlipVideoResp> flipVideo(VlinkerFlipVideoReq req) {
        return apiDeviceService.flipVideo(req);
    }


    @Override
    public DtoResult<VlinkerConvergeFlipVideoResp> getFlipVideo(VlinkerFlipVideoReq req) {
        return apiDeviceService.getFlipVideo(req);
    }

    @Override
    public DtoResult<ConvergeSdCardCapacityResp> getSdCardInfo(VlinkerSdCardInfoReq req) {
        return apiDeviceService.getSdCardInfo(req);
    }

    @Override
    public DtoResult<Void> sdCardInfoFormat(VlinkerSdCardInfoReq req) {
        return apiDeviceService.sdCardInfoFormat(req);
    }

    @Override
    public DtoResult<CommonChannelAbilityResp> getChannelAbility(ConvDeviceReq req) {
        return apiDeviceService.getChannelAbility(req);
    }

    @Override
    public DtoResult<CommonGetSideAlgorithmResp> getSideAlgorithmInfo(ConvGetSideAlgorithmReq req) {
        return apiDeviceService.getSideAlgorithmInfo(req);
    }

    @Override
    public DtoResult<CommonSetSideAlgorithmResp> setSideAlgorithmInfo(ConvSetSideAlgorithmReq deviceReq) {
        return apiDeviceService.setSideAlgorithmInfo(deviceReq);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> getNowDeviceVersionInfo(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getNowDeviceVersionInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetDeviceVersionListResp> getNowDeviceVersionListInfo(ConvDevicePageReq deviceReq) {
        return apiDeviceService.getNowDeviceVersionListInfo(deviceReq);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.deviceUpgrade(convDeviceReq);
    }

    @Override
    public DtoResult<Void> deviceRestart(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.deviceRestart(convDeviceReq);
    }

    @Override
    public DtoResult<Void> deviceReset(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.deviceReset(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getOsdInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(ConvCallDeviceByVideoReq convDeviceReq) {
        return apiDeviceService.callDeviceByVideo(convDeviceReq);
    }

    @Override
    public DtoResult<Void> voiceStatusNotifyReq(ConvVoiceStatusNotifyReq convDeviceReq) {
        return apiDeviceService.voiceStatusNotifyReq(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetDormancyResp> getDormancyParameter(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getDormancyParameter(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(ConvSetOsdInfoReq convDeviceReq) {
        return apiDeviceService.setOsdInfo(convDeviceReq);
    }

    @Override
    public DtoResult<CommonSetDormancyResp> setDormancyParameter(ConvSetDormancyParameterReq convDeviceReq) {
        return apiDeviceService.setDormancyParameter(convDeviceReq);
    }

    @Override
    public DtoResult<Void> stopCruiseTrack(VlinkerCruiseTrackReq req) {
        return apiDeviceService.stopCruiseTrack(req);
    }

    @Override
    public DtoResult<Void> delCruiseTrack(VlinkerCruiseTrackReq req) {
        return apiDeviceService.delCruiseTrack(req);
    }

    @Override
    public DtoResult<Void> startCruiseTrack(VlinkerCruiseTrackReq req) {
        return apiDeviceService.startCruiseTrack(req);
    }

    @Override
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrackList(VlinkerCruiseTrackReq req) {
        return apiDeviceService.getCruiseTrackList(req);
    }

    @Override
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrack(VlinkerCruiseTrackReq req) {
        return apiDeviceService.getCruiseTrack(req);
    }

    @Override
    @PostMapping("/feign/converge-system/snapshot")
    public DtoResult<VlinkerConvergeSnapshotResp> snapshot(DeviceSnapshotSuperReq req) {
        return apiDeviceService.deviceSnapshotSuper(req);
    }

    @Override
    @PostMapping("/feign/converge-system/querySupportPrecisePtz")
    public DtoResult<ThirdPartyPlatformsQuerySupportPrecisePtzResp> querySupportPrecisePtz(ThirdPartyPlatformsQuerySupportPrecisePtzReq req) {
        return apiDeviceService.querySupportPrecisePtz(req);
    }

    @Override
    @PostMapping("/feign/converge-system/humanGate/searchPeople")
    public DtoResult<BasePageInfoEntity<SearchPeopleByHumanGateResp>> searchPeopleByHumanGate(SearchPeopleByHumanGateReq req) {
        return apiDeviceService.searchPeopleByHumanGate(req);
    }

    @Override
    @PostMapping("/feign/converge-system/humanGate/addPeople")
    public DtoResult<Void> addPeopleByHumanGate(AddPeopleByHumanGateReq req) {
        return apiDeviceService.addPeopleByHumanGate(req);
    }

    @Override
    @PostMapping("/feign/converge-system/humanGate/deletePeople")
    public DtoResult<Void> deletePeopleByHumanGate(DeletePeopleByHumanGateReq req) {
        return apiDeviceService.deletePeopleByHumanGate(req);
    }

    /**
     * 通过门禁系统查询车辆信息。
     *
     * @param req 查询请求参数，包含门禁系统需要的搜索条件。
     * @return 返回查询结果，是一个封装了基础分页信息和车辆信息的实体。
     */
    @Override
    @PostMapping("/feign/converge-system/humanGate/searchCarInfo")
    public DtoResult<BasePageInfoEntity<QxSearchCarInfoByGateResp.CarInfo>> searchCarInfoToGate(SearchCarInfoByGateReq req) {
        // 调用apiDeviceService中的searchCarInfoToGate方法，传入请求参数，并返回查询结果
        return apiDeviceService.searchCarInfoToGate(req);
    }

    /**
     * 向门禁系统添加车辆信息。
     * <p>
     * 该接口用于将车辆信息添加到门禁系统中。通过调用 {@code apiDeviceService.addCarInfoToGate} 方法，
     * 实现对门禁系统车辆信息的添加操作。
     *
     * @param req 包含车辆信息的请求对象。该对象应包含添加车辆信息所需的所有必要字段。
     * @return 返回一个 {@code DtoResult<Void>} 对象，其中 {@code DtoResult} 是一个通用的结果容器，
     * 用于表示操作的结果状态。如果操作成功，{@code result} 字段将为 {@code null}。
     */
    @Override
    @PostMapping("/feign/converge-system/humanGate/addCarInfo")
    public DtoResult<Void> addCarInfoToGate(AddCarInfoGateReq req) {
        return apiDeviceService.addCarInfoToGate(req);
    }

    /**
     * 通过前端发送的请求删除车辆信息到门禁系统。
     *
     * @param req 包含需要删除的车辆信息的请求对象。
     * @return 返回一个包含操作结果的DtoResult对象，其中Void表示操作没有返回值。
     */
    @Override
    @PostMapping("/feign/converge-system/humanGate/deleteCarInfo")
    public DtoResult<Void> deleteCarInfoToGate(DeleteCarInfoGateReq req) {
        // 将删除车辆信息的请求转发给apiDeviceService处理
        return apiDeviceService.deleteCarInfoToGate(req);
    }

    @Override
    public DtoResult<List<ConvSysOrgEntity>> getOrgList() {
        return DtoResult.ok(convSysOrgService.list());
    }

    @Override
    public DtoResult<DeviceCapacityVo> getDeviceCapacity(GetDeviceCapacityDto dto) {
        return DtoResult.ok(apiDeviceService.getDeviceCapacity(dto.getDeviceCode()));
    }

    @Override
    public DtoResult<DeviceModelVersionVo> getDeviceModelAndVersion(GetDeviceCapacityDto dto) {
        return DtoResult.ok(apiDeviceService.getDeviceModelAndVersion(dto.getDeviceCode()));
    }

    @Override
    public DtoResult<List<JSONObject>> getDeviceModelList(List<String> deviceModelList) {
        DeviceModelEntity deviceModel = new DeviceModelEntity();
        deviceModel.setDeviceModelList(deviceModelList);
        List<DeviceModelEntity> list = deviceModelService.getList(deviceModel);
        if (CollectionUtil.isEmpty(list)) {
            return DtoResult.ok();
        }
        return DtoResult.ok(JSONObject.parseObject(JSON.toJSONString(list), new TypeReference<>() {
        }));
    }

    @Override
    public List<DeviceModelVersionEntity> getDeviceModelVersionList(DeviceModelEntity entity) {
        DeviceModelEntity deviceModel = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(DeviceModelEntity::getModel, entity.getModel()), false
        );
        if (deviceModel == null) {
            return new ArrayList<>();
        }
        DeviceModelVersionEntity deviceModelVersionEntity = new DeviceModelVersionEntity();
        deviceModelVersionEntity.setModelId(deviceModel.getId());
        return deviceModelVersionService.getList(deviceModelVersionEntity);
    }

    @Override
    public List<StorageStrategyEntity> getStorageStrategyList() {
        return storageStrategyService.list();
    }

    @Override
    public DtoResult<java.util.Map<String, Object>> getCapabilityParameters() {
        return deviceModelVersionController.getCapabilityParameters();
    }

    @Override
    public ConvDeviceAndChannelDTO getDeviceChannelList(DeviceEntity deviceEntity) {
        return apiDeviceService.getDeviceChannelList(deviceEntity);
    }

    @Override
    public DtoResult<Void> deliverTheCloudStoragePolicy(DeliverTheCloudStoragePolicyConvDto dto) {
        log.info("下发云存策略{}", JSON.toJSONString(dto));
        if (StringUtil.isEmpty(dto.getDeviceCode())) {
            log.error("请选择一个设备");
            return DtoResult.error("请选择一个设备");
        }
        if (dto.getDay() == null) {
            return DtoResult.error("请输入天数");
        }
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, dto.getDeviceCode()), false);
        if (Objects.isNull(deviceEntity)) {
            log.error("设备不存在 deviceCode:{}", dto.getDeviceCode());
            return DtoResult.error("设备不存在");
        }
        if (!Objects.equals(deviceEntity.getAccessWay(), AccessWayType.SDSDK.getType())) {
            log.error("该设备暂未实现 deviceCode:{} accessWay:{}", dto.getDeviceCode(), deviceEntity.getAccessWay());
            return DtoResult.error("该设备暂未实现");
        }
        OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, dto.getChannelId()), false);
        if (Objects.isNull(channelEntity)) {
            log.error("通道不存在 deviceId:{} channelId:{}", deviceEntity.getId(), dto.getChannelId());
            return DtoResult.error("通道不存在");
        }
        // 策略
        StorageStrategyEntity storageStrategy = storageStrategyService.getOne(new LambdaQueryWrapper<StorageStrategyEntity>()
                .eq(StorageStrategyEntity::getType, 1)
                .eq(StorageStrategyEntity::getNumValue, dto.getDay()), false);
        if (Objects.isNull(storageStrategy)) {
            log.error("策略不存在 day:{}", dto.getDay());
            return DtoResult.error(dto.getDay() + "天的策略存储不存在");
        }
        List<CloudStorageEntity> list = cloudStorageService.list(new LambdaQueryWrapper<CloudStorageEntity>()
                .eq(CloudStorageEntity::getStorageStrategyId, storageStrategy.getId()));
        if (list.isEmpty()) {
            log.error("未找到可用的云存储 storageStrategyId:{}", storageStrategy.getId());
            return DtoResult.error("未找到可用的云存储");
        }
        CloudStorageEntity cloudStorageEntity = RandomUtil.randomEle(list);
        if (Objects.isNull(cloudStorageEntity)) {
            log.error("未找到可用的云存储 storageStrategyId:{}", storageStrategy.getId());
            return DtoResult.error("未找到可用的云存储");
        }
        StringBuilder planStr = new StringBuilder();
        // 7*24小时
        for (int i = 0; i < 7 * 24; i++) {
            planStr.append("1");
        }
        if (dto.getStreamId() == null) {
            dto.setStreamId(1);
        }
        AddDeviceRecordPlanDto addDeviceRecordPlanDto = new AddDeviceRecordPlanDto();
        addDeviceRecordPlanDto.setChannelIds(Lists.newArrayList());
        addDeviceRecordPlanDto.setMon("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setTues("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setWed("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setThur("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setFri("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setSat("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setSun("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
        addDeviceRecordPlanDto.setStorageId(cloudStorageEntity.getId());
        addDeviceRecordPlanDto.setStrategyId(cloudStorageEntity.getStorageStrategyId());
        addDeviceRecordPlanDto.setRecordStreamType(dto.getStreamId());
        addDeviceRecordPlanDto.setType(1);
        addDeviceRecordPlanDto.setStatus(dto.getType() == 0 ? 2 : 1);
        addDeviceRecordPlanDto.setCloudType(addDeviceRecordPlanDto.getStatus() == 2 ? 1 : dto.getType());
        PlaybackPlanParamVideoNode build = PlaybackPlanParamVideoNode
                .builder()
                .type(1)
                .addDeviceRecordPlanDto(addDeviceRecordPlanDto)
                .enable(dto.getType())
                .channel(dto.getChannelId())
                .s3PrefixDir("r/" + cloudStorageEntity.getStorageStrategyId())
                .recordStreamType(dto.getStreamId())
                .plan(planStr.toString())
                .s3Conf(PlaybackPlanParamVideoNode.S3Conf
                        .builder()
                        .endpoint(cloudStorageEntity.getEndPoint())
                        .accessKeyId(cloudStorageEntity.getKeyId())
                        .secretKey(cloudStorageEntity.getSecret())
                        .bucketName(cloudStorageEntity.getBucket())
                        .region(cloudStorageEntity.getRegion())
                        .build())
                .build();
        build.setDeviceEntity(deviceEntity);
        build.setOpsDeviceChannelEntity(channelEntity);
        build.setChannelId(dto.getChannelId());
        build.setUserPlatformTypeEnum(OpenCommonEnum.UserPlatformType.USER_PLATFORM_TYPE_INTEGRATION_PLATFORM);
        log.info("开始下发云存配置->{}", build);
        DtoResult<Void> voidDtoResult = videoCommonService.setCloudPlaybackPlan(build);
        if (!voidDtoResult.success()) {
            log.error("设置云存失败，errMsg:{}, err:{}", voidDtoResult.getMessage(), voidDtoResult.getError());
            return DtoResult.error("设置云存失败，errMsg:" + voidDtoResult.getMessage(), voidDtoResult.getError());
        }
        // 根据设备ID查询现有的录像计划，如果不存在，则创建新的录像计划；如果存在，则更新录像计划
        DeviceRecordPlanEntity plan = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, deviceEntity.getId())
                .eq(DeviceRecordPlanEntity::getChannelId, dto.getChannelId()), false);
        if (plan == null) {
            // 新增录像计划
            plan = new DeviceRecordPlanEntity();
            plan.setStorageId(cloudStorageEntity.getId());
            plan.setStrategyId(cloudStorageEntity.getStorageStrategyId());
            plan.setId(IdWorker.getId());
            plan.setDeviceId(deviceEntity.getId());
            plan.setChannelId(dto.getChannelId());
            plan.setChannelIdLong(channelEntity.getId());
            plan.setCreateTime(DateTime.now());
            plan.setCreateUser(JwtUtil.getUserId());
            plan.setMon("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setTues("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setWed("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setThur("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setFri("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setSat("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setSun("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setCloudType(addDeviceRecordPlanDto.getCloudType());
            plan.setStatus(addDeviceRecordPlanDto.getStatus());
            plan.setType(1);
            plan.setRecordStreamType(addDeviceRecordPlanDto.getRecordStreamType());
            deviceRecordPlanService.save(plan);
        } else {
            // 更新录像计划
            plan.setStorageId(cloudStorageEntity.getId());
            plan.setStrategyId(cloudStorageEntity.getStorageStrategyId());
            plan.setUpdateTime(DateTime.now());
            plan.setUpdateUser(JwtUtil.getUserId());
            plan.setMon("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setTues("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setWed("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setThur("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setFri("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setSat("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setSun("0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23");
            plan.setCloudType(addDeviceRecordPlanDto.getCloudType());
            plan.setStatus(addDeviceRecordPlanDto.getStatus());
            plan.setType(1);
            plan.setRecordStreamType(addDeviceRecordPlanDto.getRecordStreamType());
            deviceRecordPlanService.updateById(plan);
        }
        log.info("更新设备录像计划成功");
        return DtoResult.ok();
    }

    @Override
    public DtoResult<List<DeviceInfoEntity>> syncDeviceAndChannelName(List<DeviceInfoEntity> deviceInfoEntityList) {
        return apiDeviceService.syncDeviceAndChannelName(deviceInfoEntityList);
    }

    @Override
    public DtoResult<java.util.Map<String, Object>> getAllAlarmParameters() {
        return opsDeviceAlarmController.getAllAlarmParameters();
    }

    @Override
    public DtoResult<SaveOrUpdateCruiseTrackResp> saveOrUpdateCruiseTrack(ConvSaveOrUpdateCruiseTrackReq req) {
        return apiDeviceService.saveOrUpdateCruiseTrack(req);
    }

    @Override
    public DtoResult<DeviceEntity> getDeviceInfoByDeviceCode(DeviceEntity deviceEntity) {
        if (StringUtil.isEmpty(deviceEntity.getDeviceCode())) {
            return DtoResult.error("请选择一个设备");
        }
        DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, deviceEntity.getDeviceCode()), false);
        return DtoResult.ok(device);
    }

    @Override
    public List<DeviceModelVersionVo> getDeviceModel(List<String> deviceCodeCodes) {
        List<DeviceEntity> list = deviceService.list(new LambdaQueryWrapper<DeviceEntity>().in(DeviceEntity::getDeviceCode, deviceCodeCodes));
        List<DeviceModelVersionVo> deviceModelVersions = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (DeviceEntity deviceEntity : list) {
                DeviceModelVersionVo build = DeviceModelVersionVo.builder().deviceCodeCode(deviceEntity.getDeviceCode()).model(deviceEntity.getModel()).macAddress(deviceEntity.getMac()).build();
                deviceModelVersions.add(build);
            }
        }
        return deviceModelVersions;
    }


    @Override
    public DtoResult<Void> setDualCameraLinkage(ConvSetDualCameraLinkageReq convDeviceReq) {
        return apiDeviceService.setDualCameraLinkage(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setSoundAndLightShock(ConvSetSoundAndLightShockReq convDeviceReq) {
        return apiDeviceService.setSoundAndLightShock(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetSoundAndLightShockReq> getSoundAndLightShock(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getSoundAndLightShock(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setVolumeCommand(ConvSetVolumeCommandReq convDeviceReq) {
        return apiDeviceService.setVolumeCommand(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetVolumeCommandResp> getVolumeCommand(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getVolumeCommand(convDeviceReq);
    }

    @Override
    public DtoResult<CommonGetHumanoidMarkersReq> getHumanoidMarkers(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getHumanoidMarkers(convDeviceReq);
    }

    @Override
    public DtoResult<Void> oneClickPatrol(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.oneClickPatrol(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setHumanoidMarkers(ConvSetHumanoidMarkersReq convDeviceReq) {
        return apiDeviceService.setHumanoidMarkers(convDeviceReq);
    }

    @Override
    public DtoResult<Void> gimbalCalibration(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.gimbalCalibration(convDeviceReq);
    }

    @Override
    public DtoResult<Void> setDevicRelativeXyz(ConvSetDevicRelativeXyzReq convDeviceReq) {
        return apiDeviceService.setDevicRelativeXyz(convDeviceReq);
    }

    @Override
    @PostMapping("/feign/converge-system/getScreenInfo")
    public DtoResult<CommonGetScreenPropertiesResp> getScreenInfo(ConvDeviceReq convDeviceReq) {
        return apiDeviceService.getScreenInfo(convDeviceReq);
    }

    @Override
    @PostMapping("/feign/converge-system/setScreenInfo")
    public DtoResult<Void> setScreenInfo(ConvSetScreenPropertiesReq convDeviceReq) {
        return apiDeviceService.setScreenInfo(convDeviceReq);
    }
}