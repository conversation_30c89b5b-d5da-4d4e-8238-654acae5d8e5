package com.saida.services.system.video.indexer;

import com.saida.services.system.video.algHall.saidaPlayer.Message;
import lombok.Data;

/**
 * Mirror of C struct MEDIA_INDEXER_KEY_FRAME_INFO.
 * Layout (little-endian):
 * - Message.Header (12 bytes)
 * - uint32_t pos (4 bytes)
 *
 * In Message.Header, contentLength should be 4 for this structure (body only).
 */
@Data
public class MediaIndexerKeyFrameInfo {
    public static final int BODY_SIZE = 4;

    private final Message.Header header;
    private final long pos; // uint32

    public MediaIndexerKeyFrameInfo(Message.Header header, long pos) {
        this.header = header;
        this.pos = pos;
    }

}
