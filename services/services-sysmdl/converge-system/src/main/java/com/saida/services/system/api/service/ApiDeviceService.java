package com.saida.services.system.api.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.deviceApi.req.*;
import com.saida.services.converge.dto.*;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.ConvDeviceAndChannelDTO;
import com.saida.services.converge.entity.dto.DeviceAndChannelDto;
import com.saida.services.converge.entity.dto.DeviceChannelDto;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.entity.params.OpsDeviceParams;
import com.saida.services.converge.enums.AccessWayType;
import com.saida.services.converge.enums.SdkAccessType;
import com.saida.services.converge.qxNode.QxNodeApiEnum;
import com.saida.services.converge.qxNode.req.*;
import com.saida.services.converge.qxNode.req.AddPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.DeletePeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.SearchPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.device.GetChannelRecordDownloadReq;
import com.saida.services.converge.qxNode.req.gb2022.*;
import com.saida.services.converge.qxNode.req.qx.QxAddPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.qx.QxDeletePeopleByHumanGateReq;
import com.saida.services.converge.qxNode.req.qx.QxSearchPeopleByHumanGateReq;
import com.saida.services.converge.qxNode.resp.*;
import com.saida.services.converge.qxNode.resp.gb2022.DeviceConfigResp;
import com.saida.services.converge.qxNode.resp.qx.QxSearchCarInfoByGateResp;
import com.saida.services.converge.qxNode.resp.qx.QxSearchPeopleByHumanGateResp;
import com.saida.services.converge.qxNode.resp.qx.SDCardInfoResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeSdCardCapacityResp;
import com.saida.services.converge.vo.*;
import com.saida.services.deviceApi.req.*;
import com.saida.services.deviceApi.resp.*;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.biz.req.ThirdPartyPlatformsQuerySupportPrecisePtzReq;
import com.saida.services.open.biz.resp.ThirdPartyPlatformsQuerySupportPrecisePtzResp;
import com.saida.services.open.dto.DeviceCapacityDto;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.enums.DeviceCapacityEnum;
import com.saida.services.open.req.*;
import com.saida.services.open.resp.*;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.api.param.RtcConnParam;
import com.saida.services.system.client.nodev1.QxNodeReqService;
import com.saida.services.system.client.nodev1.QxNodeReqUtil;
import com.saida.services.system.client.nodev1.ResponseDto;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.video.deviceSdkAccessService.DeviceSdkAccessService;
import com.saida.services.system.video.param.*;
import com.saida.services.system.video.param.CtrlHomePositionReq;
import com.saida.services.system.video.service.VideoCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ApiDeviceService {

    @Resource
    private DeviceService deviceService;
    @Resource
    private SignalNodeService signalNodeService;
    @Resource
    private QxNodeReqService qxNodeReqService;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Resource
    private QxNodeReqUtil qxNodeReqUtil;
    @Resource
    private DeviceModelVersionService deviceModelVersionService;
    @Resource
    private DeviceModelService deviceModelService;
    @Resource
    private VideoCommonService videoCommonService;

    /**
     * 根据设备状态分页查询设备及其通道的信息。
     * <p>
     * 本方法通过调用deviceService的listPageByOpen方法，根据设备实体的状态查询设备及其通道的信息。
     * 主要用于在前端页面上展示设备的分页列表，根据设备是否开启来筛选数据。
     *
     * @param entity 设备实体，包含设备的状态等信息，用于查询条件。
     * @return 返回分页查询结果，包含设备及其通道的信息。
     */
    public BasePageInfoEntity<DeviceAndChannelDto> listPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest) {
        return deviceService.listPageByOpen(entity, baseRequest);
    }

    /**
     * 根据设备状态分页查询设备及其通道的信息。
     * <p>
     * 本方法通过调用deviceService的listPageByOpen方法，根据设备实体的状态查询设备及其通道的信息。
     * 主要用于在前端页面上展示设备的分页列表，根据设备是否开启来筛选数据。
     *
     * @param entity 设备实体，包含设备的状态等信息，用于查询条件。
     * @return 返回分页查询结果，包含设备及其通道的信息。
     */
    public BasePageInfoEntity<DeviceAndChannelDto> deviceListPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest) {
        return deviceService.deviceListPageByOpen(entity, baseRequest);
    }

    /**
     * 根据设备序列号获取设备通道列表。
     *
     * @param sn 设备的序列号，用于查询设备的通道信息。
     * @return 返回设备的通道列表，如果查询结果为空，则返回一个空列表。
     */
    public List<DeviceChannelDto> getChannelList(String sn) {
        // 使用LambdaQueryWrapper查询与设备序列号匹配的通道信息
        List<OpsDeviceChannelEntity> list = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, sn));

        // 如果查询结果为空，则直接返回一个空列表
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 初始化用于存储转换后数据的列表
        List<DeviceChannelDto> result = new ArrayList<>();

        // 遍历查询结果，将OpsDeviceChannelEntity对象转换为DeviceChannelDto对象，并添加到结果列表中
        list.forEach(c -> {
            result.add(new DeviceChannelDto() {{
                setChannelId(c.getChannelId());
                setChannelName(c.getChannelName());
                // 将更新时间格式化为指定的字符串格式
                setUpdateTime(c.getUpdateTime());
            }});
        });

        // 返回转换后的通道列表
        return result;
    }

    /**
     * 根据设备SN和通道ID获取直播URL。
     *
     * @param req 包含设备SN和通道ID的请求对象。
     * @return 如果请求成功，返回包含直播URL的DTO；如果失败，返回错误信息。
     */
    public DtoResult<StreamUrlDto> getLiveUrl(VlinkerConvergeVideoLiveUrlReq req) {

        // 检查通道ID是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        DeviceEntity device = null;
        if (req.getDeviceId() != null) {
            device = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        if (req.getMute() == null) {
            req.setMute(false);
        }
        OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, device.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (channelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        GetStreamUrlParamVideoNode getStreamUrlParamVideoNode = new GetStreamUrlParamVideoNode();
        getStreamUrlParamVideoNode.setDeviceEntity(device);
        getStreamUrlParamVideoNode.setOpsDeviceChannelEntity(channelEntity);
        getStreamUrlParamVideoNode.setChannelId(req.getChannelId());
        getStreamUrlParamVideoNode.setChannelXId(channelEntity.getNodeXId());
        getStreamUrlParamVideoNode.setMute(req.getMute());
        getStreamUrlParamVideoNode.setStreamIndex(req.getStreamIndex());
        getStreamUrlParamVideoNode.setNetworking(req.getNetworking());
        getStreamUrlParamVideoNode.setActiveSecond(req.getActiveSecond());
        getStreamUrlParamVideoNode.setVideoProtocol(req.getVideoProtocol());
        getStreamUrlParamVideoNode.setOpenAi(req.getOpenAi());
        getStreamUrlParamVideoNode.setStartAi(req.getStartAi());
        return videoCommonService.getStreamUrl(getStreamUrlParamVideoNode);
    }

    public DtoResult<VlinkerConvergeSnapshotResp> deviceSnapshotSuper(DeviceSnapshotSuperReq req) {
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道必传");
        }
        DeviceEntity device = null;
        if (req.getDeviceId() != null) {
            device = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        GetSnapshotParam getSnapshotParam = new GetSnapshotParam();

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, device.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        getSnapshotParam.setChannelXId(opsDeviceChannelEntity.getNodeXId());
        getSnapshotParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        getSnapshotParam.setDeviceEntity(device);
        getSnapshotParam.setDeviceId(device.getId());
        getSnapshotParam.setChannelId(req.getChannelId());
        getSnapshotParam.setGenerateFlag(req.getGenerateFlag());
        getSnapshotParam.setDeviceCode(device.getDeviceCode());
        getSnapshotParam.setTimes(req.getTimes());
        getSnapshotParam.setWidth(req.getWidth());
        return videoCommonService.getSnapshot(getSnapshotParam);
    }

    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonths(GetChannelRecordMonthsParamVideoNode paramVideoNode) {
        DeviceEntity device = null;
        if (paramVideoNode.getDeviceId() != null) {
            device = deviceService.getById(paramVideoNode.getDeviceId());
        } else if (StringUtil.isNotEmpty(paramVideoNode.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, paramVideoNode.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        if (StringUtil.isEmpty(paramVideoNode.getChannelId())) {
            return DtoResult.error("通道必传");
        }
        if (StringUtil.isEmpty(paramVideoNode.getSource())) {
            // CLOUD: 云存录像; LOCAL: 卡存录像
            paramVideoNode.setSource("CLOUD");
        }
        if (StringUtil.isEmpty(paramVideoNode.getDates())) {
            paramVideoNode.setDates(DateTime.now().toString("yyyyMM"));
        }
        paramVideoNode.setDeviceEntity(device);
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, device.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, paramVideoNode.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        paramVideoNode.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        paramVideoNode.setDeviceId(device.getId());
        paramVideoNode.setChannelXId(opsDeviceChannelEntity.getNodeXId());
        if ("CLOUD".equals(paramVideoNode.getSource())) {
            return videoCommonService.getChannelRecordMonthsByCloud(paramVideoNode);
        } else {
            return videoCommonService.getChannelRecordMonthsByLocal(paramVideoNode);
        }
    }

    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLine(GetChannelRecordTimeLineParamVideoNode paramVideoNode) {
        DeviceEntity device = null;
        if (paramVideoNode.getDeviceId() != null) {
            device = deviceService.getById(paramVideoNode.getDeviceId());
        }
        // 检查设备SN是否为空，为空则返回错误信息
        if (StringUtil.isNotEmpty(paramVideoNode.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, paramVideoNode.getDeviceCode()), false);
        }
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道ID是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(paramVideoNode.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // CLOUD: 云存录像; LOCAL: 卡存录像
        if (StringUtil.isEmpty(paramVideoNode.getSource())) {
            paramVideoNode.setSource("CLOUD");
        }
        if (paramVideoNode.getStart() == null || paramVideoNode.getEnd() == null) {
            return DtoResult.error("开始结束时间戳必传");
        }
        paramVideoNode.setDeviceEntity(device);
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, device.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, paramVideoNode.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        paramVideoNode.setDeviceId(device.getId());
        paramVideoNode.setChannelXId(opsDeviceChannelEntity.getNodeXId());
        paramVideoNode.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        if ("CLOUD".equals(paramVideoNode.getSource())) {
            return videoCommonService.getChannelRecordTimeLineByCloud(paramVideoNode);
        } else {
            return videoCommonService.getChannelRecordTimeLineByLocal(paramVideoNode);
        }
    }

    public DtoResult<BasePageInfoEntity<ChannelRecordTimeLine>> getChannelRecordTimeLinePage(GetChannelRecordTimeLineParamVideoNode paramVideoNode, BaseRequest baseRequest) {
        DeviceEntity device = null;
        if (paramVideoNode.getDeviceId() != null) {
            device = deviceService.getById(paramVideoNode.getDeviceId());
        }
        // 检查设备SN是否为空，为空则返回错误信息
        if (StringUtil.isNotEmpty(paramVideoNode.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, paramVideoNode.getDeviceCode()), false);
        }
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道ID是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(paramVideoNode.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // CLOUD: 云存录像; LOCAL: 卡存录像
        if (StringUtil.isEmpty(paramVideoNode.getSource())) {
            paramVideoNode.setSource("CLOUD");
        }
        if (paramVideoNode.getStart() == null || paramVideoNode.getEnd() == null) {
            return DtoResult.error("开始结束时间戳必传");
        }
        paramVideoNode.setDeviceEntity(device);
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, device.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, paramVideoNode.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        paramVideoNode.setChannelXId(opsDeviceChannelEntity.getNodeXId());
        paramVideoNode.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        paramVideoNode.setDeviceId(device.getId());
        DtoResult<List<ChannelRecordTimeLine>> channelRecordTimeLine;
        if ("CLOUD".equals(paramVideoNode.getSource())) {
            channelRecordTimeLine = videoCommonService.getChannelRecordTimeLineByCloud(paramVideoNode);
        } else {
            channelRecordTimeLine = videoCommonService.getChannelRecordTimeLineByLocal(paramVideoNode);
        }
        if (!channelRecordTimeLine.success()) {
            return DtoResult.error(channelRecordTimeLine.getMessage());
        }

        int pageNum = baseRequest.getPageNum();
        int pageSize = baseRequest.getPageSize();

        int totalSize = channelRecordTimeLine.getData().size();
        // 计算分页的起始索引
        int startIndex = (pageNum - 1) * pageSize;
        // 计算分页的结束索引（需要加1是因为endIndex是开区间）
        int endIndex = Math.min(startIndex + pageSize, totalSize);
        List<ChannelRecordTimeLine> resultList = channelRecordTimeLine.getData().subList(startIndex, endIndex);
        BasePageInfoEntity<ChannelRecordTimeLine> basePageInfoEntity = new BasePageInfoEntity<>(baseRequest.getPageNum(), baseRequest.getPageSize(), totalSize, resultList);
        return DtoResult.ok(basePageInfoEntity);
    }

    public DtoResult<VlinkerConvergeHomePositionResp> ctrlHomePosition(CtrlHomePositionReq req) {
        if (null == req.getDeviceId()) {
            return DtoResult.error("deviceId为空！");
        }
        if (req.getPresetIndex() == null) {
            return DtoResult.error("预置点位必传");
        }
        if (StringUtil.isEmpty(req.getResetTime())) {
            return DtoResult.error("预置点位名称必传");
        }
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道id必传");
        }
        DeviceEntity deviceEntity = deviceService.getById(req.getDeviceId());
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
        if (node == null) {
            return DtoResult.error("设备未绑定节点！");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId())
                .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                .last(" limit 1 "));
        if (one == null) {
            return DtoResult.error("设备未配置通道");
        }
        String channelId = one.getChannelId();
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道id不存在");
        }
        CtrlHomePositionReq param = new CtrlHomePositionReq();
        param.setOpsDeviceChannelEntity(one);
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setEnabled(req.getEnabled());
        param.setResetTime(req.getResetTime());
        param.setPresetIndex(req.getPresetIndex());
        return videoCommonService.setHomePosition(param);
    }

    public DtoResult<Void> stopLive(VlinkerConvergeVideoLiveUrlReq paramVideoNode) {
        DeviceEntity device = null;
        if (paramVideoNode.getDeviceId() != null) {
            device = deviceService.getById(paramVideoNode.getDeviceId());
        }
        // 检查设备SN是否为空，为空则返回错误信息
        if (StringUtil.isNotEmpty(paramVideoNode.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, paramVideoNode.getDeviceCode()), false);
        }
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道ID是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(paramVideoNode.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        VideoNodeBaseParam baseParam = new VideoNodeBaseParam();
        baseParam.setDeviceEntity(device);
        baseParam.setDeviceId(device.getId());
        baseParam.setDeviceCode(device.getDeviceCode());
        baseParam.setChannelId(paramVideoNode.getChannelId());
        return videoCommonService.stopLive(baseParam);
    }


    public DtoResult<VlinkerConvergeVirtuallyDataResp> getVirtuallyData(VlinkerConvergeVirtuallyDataReq req) {
        // 检查设备SN是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getDeviceSN())) {
            return DtoResult.error("设备码必传");
        }
        // 检查通道ID是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 根据设备SN查询设备信息
        DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode, req.getDeviceSN()), false);
        // 如果设备不存在，则返回错误信息
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        if (!Objects.equals(device.getAccessWay(), AccessWayType.VIRTUALLY.getType())) {
            return DtoResult.error("设备不是虚拟设备");
        }
        // 根据设备ID查询信号节点信息
        SignalNodeEntity node = signalNodeService.getById(device.getNodeId());
        // 如果设备未绑定信号节点，则返回错误信息
        if (node == null) {
            return DtoResult.error("设备未绑定节点");
        }

        OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, device.getDeviceCode())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (channelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        VlinkerConvergeVirtuallyDataResp resp = new VlinkerConvergeVirtuallyDataResp();
        resp.setMode(channelEntity.getMode());
        resp.setRtmpUrl(channelEntity.getRtmpUrl());
        resp.setRtspUrl(channelEntity.getRtspUrl());
        resp.setPullUrl(channelEntity.getPullUrl());
        return DtoResult.ok(resp);

    }

    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrl(GetChannelRecordTimeLineParamVideoNode paramVideoNode) {
        DeviceEntity device = null;
        if (paramVideoNode.getDeviceId() != null) {
            device = deviceService.getById(paramVideoNode.getDeviceId());
        } else if (StringUtil.isNotEmpty(paramVideoNode.getDeviceCode())) {
            // 根据设备SN查询设备信息
            device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, paramVideoNode.getDeviceCode()), false);
        }
        if (device == null) {
            return DtoResult.error("设备不存在");
        }
        if (StringUtil.isEmpty(paramVideoNode.getChannelId())) {
            return DtoResult.error("通道必传");
        }
        if (StringUtil.isEmpty(paramVideoNode.getSource())) {
            // CLOUD: 云存录像; LOCAL: 卡存录像
            paramVideoNode.setSource("CLOUD");
        }
        if (paramVideoNode.getStart() == null || paramVideoNode.getEnd() == null) {
            return DtoResult.error("开始结束时间戳必传");
        }
        paramVideoNode.setDeviceEntity(device);
        OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, device.getDeviceCode())
                .eq(OpsDeviceChannelEntity::getChannelId, paramVideoNode.getChannelId()));
        if (channelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        paramVideoNode.setOpsDeviceChannelEntity(channelEntity);
        paramVideoNode.setDeviceId(device.getId());
        paramVideoNode.setChannelXId(channelEntity.getNodeXId());
        if (paramVideoNode.getSource().equals("CLOUD")) {
            return videoCommonService.getChannelRecordUrlByCloud(paramVideoNode);
        } else {
            return videoCommonService.getChannelRecordUrlByLocal(paramVideoNode);
        }
    }

    public DtoResult<GbStopDownloadLocalPlaybackVo> stopDownloadPlayback(DownloadPlaybackDto dto) {
        Long deviceId = dto.getDeviceId();
        String channelId = dto.getChannelId();
        if (deviceId == null) {
            return DtoResult.error("设备ID必传");
        }
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }
        SignalNodeEntity node = signalNodeService.getSignalNodeByDeviceId(deviceId);
        String ssrc = dto.getUuid();
        GetChannelRecordDownloadReq getChannelRecordTimeLineReq = new GetChannelRecordDownloadReq();
        getChannelRecordTimeLineReq.setChannelId(dto.getChannelId());
        getChannelRecordTimeLineReq.setStart(dto.getStart());
        getChannelRecordTimeLineReq.setEnd(dto.getEnd());
        getChannelRecordTimeLineReq.setSsrc(ssrc);
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.STOP_DOWNLOAD_PLAYBACK, node, getChannelRecordTimeLineReq);
        if (responseDto.getHttpCode() != 200) {
            return DtoResult.error(QxNodeApiEnum.STOP_DOWNLOAD_PLAYBACK.getDes() + "失败", responseDto.getMsg());
        }
        GbStopDownloadPlaybackResp resp = JSON.parseObject(responseDto.getRes(), GbStopDownloadPlaybackResp.class);
        if (resp == null) {
            return DtoResult.error();
        }
        GbStopDownloadLocalPlaybackVo gbStopDownloadLocalPlaybackVo = new GbStopDownloadLocalPlaybackVo();
        gbStopDownloadLocalPlaybackVo.setChannelId(resp.getChannelId());
        gbStopDownloadLocalPlaybackVo.setDownloadUrl(resp.getDownloadUrl());
        return DtoResult.ok(gbStopDownloadLocalPlaybackVo);
    }

    public DtoResult<Void> stopPlayback(StopPlaybackDto dto) {

        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(dto.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        DeviceEntity deviceEntity = null;
        if (dto.getDeviceId() != null) {
            deviceEntity = deviceService.getById(dto.getDeviceId());
        } else if (StringUtil.isNotEmpty(dto.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, dto.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, dto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceEntity(deviceEntity);
        param.setDeviceCode(deviceEntity.getDeviceCode());
        param.setChannelId(dto.getChannelId());
        param.setDeviceEntity(deviceEntity);
        CommonStopPlayBackReq backReq = new CommonStopPlayBackReq();
        backReq.setUuid(dto.getUuid());
        return videoCommonService.stopPlayback(param, backReq);
    }

    public DtoResult<GbControlLocalPlaybackVo> controlPlayback(ControlPlaybackDto dto) {
        Long deviceId = dto.getDeviceId();
        String channelId = dto.getChannelId();
        if (deviceId == null) {
            return DtoResult.error("设备ID必传");
        }
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }
        SignalNodeEntity node = signalNodeService.getSignalNodeByDeviceId(deviceId);
        String ssrc = dto.getUuid();
        ControlPlaybackResp resp = qxNodeReqService.controlPlayback(node, channelId, null, dto.getScale(), ssrc);
        if (resp == null) {
            return DtoResult.error();
        }
        GbControlLocalPlaybackVo gbControlLocalPlaybackVo = new GbControlLocalPlaybackVo();
        gbControlLocalPlaybackVo.setChannelId(resp.getChannel_id());
        gbControlLocalPlaybackVo.setScale(resp.getScale());
        return DtoResult.ok(gbControlLocalPlaybackVo);
    }

    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackDto dto) {
        Long deviceId = dto.getDeviceId();
        String channelId = dto.getChannelId();
        if (deviceId == null) {
            return DtoResult.error("设备ID必传");
        }
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, dto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        DownloadPlaybackParamVideoNode param = new DownloadPlaybackParamVideoNode();
        param.setDeviceCode(deviceEntity.getDeviceCode());
        param.setDeviceId(deviceEntity.getId());
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        param.setChannelId(dto.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setStartTime(dto.getStart());
        param.setEndTime(dto.getEnd());
        return videoCommonService.downloadPlayback(param);
    }


    public DtoResult<VlinkerConvergeGetPtzResp> getPtz(VlinkerConvergeGetPtzReq req) {
        // 检查设备序列号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getDeviceCode())) {
            return DtoResult.error("设备必选");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 根据设备序列号获取设备信息
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode,
                req.getDeviceCode()), false);
        // 检查设备信息是否存在，不存在则返回错误信息
        if (ObjectUtil.isEmpty(deviceEntity)) {
            return DtoResult.error("查无此设备");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getPtz(param);
    }


    public DtoResult<Void> setPtz(VlinkerConvergeSetPtzReq req) {
        // 检查设备序列号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getDeviceCode())) {
            return DtoResult.error("设备必选");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 根据设备序列号获取设备信息
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode,
                req.getDeviceCode()), false);
        // 检查设备信息是否存在，不存在则返回错误信息
        if (ObjectUtil.isEmpty(deviceEntity)) {
            return DtoResult.error("查无此设备");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        SetPTZParamVideoNode param = new SetPTZParamVideoNode();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        param.setPan(req.getPan());
        param.setTilt(req.getTilt());
        param.setZoom(req.getZoom());
        return videoCommonService.setPtz(param);
    }

    public DtoResult<CommonSetStatusLightResp> getFillLightStatus(VlinkerConvergeFillLightStatusReq req) {
        // 检查设备序列号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getDeviceCode())) {
            return DtoResult.error("设备必选");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 根据设备序列号获取设备信息
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode,
                req.getDeviceCode()), false);
        // 检查设备信息是否存在，不存在则返回错误信息
        if (ObjectUtil.isEmpty(deviceEntity)) {
            return DtoResult.error("查无此设备");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getLightingArg(param);
    }

    public DtoResult<VlinkerConvergeFillLightStatusResp> setFillLightStatus(VlinkerConvergeFillLightStatusReq req) {
        // 检查设备序列号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getDeviceCode())) {
            return DtoResult.error("设备必选");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 根据设备序列号获取设备信息
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode,
                req.getDeviceCode()), false);
        // 检查设备信息是否存在，不存在则返回错误信息
        if (ObjectUtil.isEmpty(deviceEntity)) {
            return DtoResult.error("查无此设备");
        }
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        SetFillLightingArgParamVideoNode param = new SetFillLightingArgParamVideoNode();
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        param.setStatusLight(req.getStatusLight());
        param.setMode(req.getMode());
        param.setValue(req.getValue());
        return videoCommonService.setFillLightingArg(param);
    }

    public DtoResult<VlinkerConvergeHomePositionResp> getHomePosition(VlinkerConvergeHomePositionReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在");
        }
        CtrlHomePositionReq param = new CtrlHomePositionReq();
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setChannelXId(one.getNodeXId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(one);
        return videoCommonService.getHomePosition(param);
    }

    public DtoResult<VlinkerConvergeHomePositionResp> setHomePosition(VlinkerConvergeHomePositionReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在");
        }
        CtrlHomePositionReq param = new CtrlHomePositionReq();
        param.setDeviceCode(req.getDeviceCode());
        param.setChannelId(req.getChannelId());
        param.setChannelXId(one.getNodeXId());
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(one);
        param.setEnabled(req.getEnabled());
        param.setResetTime(req.getResetTime());
        param.setPresetIndex(req.getPresetIndex());
        return videoCommonService.setHomePosition(param);
    }

    /**
     * 对云台进行控制操作。
     *
     * @param req 控制请求的参数对象，包含设备序列号、通道号、控制动作和方向等信息。
     * @return 返回操作结果，如果操作失败，则包含错误信息。
     */
    public DtoResult<Void> ptzCmd(VlinkerConvergePtzControlReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 检查通道号是否为空，为空则返回错误信息
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        // 检查动作和方向是否为空，为空则返回错误信息
        if (req.getAction() == null || StringUtil.isEmpty(req.getDirection())) {
            return DtoResult.error("云台控制参数错误");
        }

        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在");
        }
        PtzCmdParamVideoNode paramVideoNode = new PtzCmdParamVideoNode();
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(req.getChannelId());
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setAction(req.getAction());
        paramVideoNode.setDirection(req.getDirection());
        paramVideoNode.setSpeed(req.getSpeed());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        // 这个地方 不知道什么好的方式
        paramVideoNode.setChannelNum(one.getChannelName().contains("可见光") ? 0 : 1);
        return videoCommonService.ptzCmd(paramVideoNode);
    }


    /**
     * 获取预置点位列表
     *
     * @param req 请求对象，包含设备序列号（DeviceSN）和通道编号（ChannelId）
     * @return 返回预置点位列表的响应对象，如果请求失败，会包含错误信息
     * 此方法首先验证请求对象中的设备序列号和通道编号是否为空，
     * 如果为空，则返回错误信息。然后根据设备序列号查询设备信息，
     * 如果设备不存在，则返回错误信息。接着，通过设备ID查询信号节点信息，
     * 并调用接口获取预置点位列表。如果获取预置点位失败，会返回相应的错误信息。
     * 最后，将获取到的预置点位转换为响应对象列表，并返回成功响应。
     */
    public DtoResult<List<VlinkerConvergePreSetListResp>> preSetList(VlinkerPreSetListReq req) {
        // 验证通道编号是否为空
        if (StringUtil.isEmpty(req.getChannelId())) {
            return DtoResult.error("通道码必传");
        }
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity opsDeviceChannel = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (opsDeviceChannel == null) {
            return DtoResult.error("通道不存在");
        }
        VideoNodeBaseParam paramVideoNode = new VideoNodeBaseParam();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(req.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(opsDeviceChannel);
        return videoCommonService.preSetList(paramVideoNode);
    }


    /**
     * 设置预置点位
     *
     * @param req 设置预置点位的请求参数，包含设备序列号（DeviceSN）、预置点位索引（Index）、预置点位名称（Name）等信息。
     * @return 返回操作结果，如果操作成功，返回一个空的DtoResult；如果操作失败，返回包含错误信息的DtoResult。
     * 此方法首先验证请求参数的合法性，确保设备序列号、预置点位索引和预置点位名称都不为空。
     * 然后根据设备序列号查询设备信息，如果设备不存在，则返回错误信息。
     * 接着，通过设备ID查询信号节点信息，并调用qxNodeReqService的preSet方法来设置预置点位。
     * 最后，无论操作是否成功，都返回一个表示操作结果的DtoResult对象。
     */
    public DtoResult<Void> preSet(VlinkerSetPreSetReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        if (req.getChannelId() == null) {
            return DtoResult.error("通道码必传");
        }
        // 验证预置点位索引是否为空
        if (req.getIndex() == null) {
            return DtoResult.error("预置点位必传");
        }
        // 验证预置点位名称是否为空
        if (StringUtil.isEmpty(req.getName())) {
            return DtoResult.error("预置点位名称必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        PreSetParamVideoNode paramVideoNode = new PreSetParamVideoNode();
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(req.getChannelId());
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        paramVideoNode.setIndex(req.getIndex());
        paramVideoNode.setName(req.getName());
        return videoCommonService.preSet(paramVideoNode);

    }


    /**
     * 跳转到预置点位的接口。
     * <p>
     * 该方法用于处理设备跳转到预置点位的请求。首先，它验证请求中是否提供了设备序列号（DeviceSN）和预置点位索引，
     * 如果这些信息缺失，则返回错误结果。然后，它尝试根据设备序列号查找设备实体，如果设备不存在，则返回错误结果。
     * 接下来，它获取与设备关联的信号节点实体，并调用qxNodeReqService的preSetJump方法来执行跳转到预置点位的操作。
     * 最后，如果所有操作都成功，则返回成功结果。
     *
     * @param req 跳转预置点位的请求对象，包含设备序列号、预置点位索引和通道ID等信息。
     * @return 如果操作成功，则返回一个空的成功结果；否则，返回包含错误信息的结果。
     */
    public DtoResult<Void> preSetJump(VlinkerJumpPreSetReq req) {
        // 验证预置点位索引是否为空
        if (req.getIndex() == null) {
            return DtoResult.error("预置点位必传");
        }
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在");
        }
        PreSetParamVideoNode paramVideoNode = new PreSetParamVideoNode();
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(req.getChannelId());
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setIndex(req.getIndex());
        // 这个地方 不知道什么好的方式
        paramVideoNode.setChannelNum(one.getChannelName().contains("可见光") ? 0 : 1);
        return videoCommonService.preSetJump(paramVideoNode);
    }


    /**
     * 删除预置点位
     *
     * @param req 删除预置点位的请求对象，包含设备序列号（DeviceSN）、预置点位索引（Index）、通道ID（ChannelId）等信息。
     * @return 返回一个DtoResult对象，其中包含操作结果。如果操作成功，返回一个空的DtoResult；如果操作失败，返回包含错误信息的DtoResult。
     * <p>
     * 此方法首先检查请求对象中是否提供了设备序列号和预置点位索引，如果缺少这些信息，则返回错误信息。
     * 然后，根据设备序列号查询设备信息。如果设备不存在，则返回错误信息。
     * 接着，根据设备ID查询信号节点信息，并调用qxNodeReqService的preSetDelete方法删除预置点位。
     * 最后，无论操作是否成功，都返回一个表示操作成功的DtoResult对象。
     */
    public DtoResult<Void> preSetDelete(VlinkerDelPreSetReq req) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在");
        }
        // 检查预置点位索引是否为空，如果为空，则返回错误信息。
        if (req.getIndex() == null) {
            return DtoResult.error("预置点位必传");
        }
        PreSetParamVideoNode paramVideoNode = new PreSetParamVideoNode();
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(req.getChannelId());
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setIndex(req.getIndex());
        paramVideoNode.setChannelNum(one.getChannelName().contains("可见光") ? 0 : 1);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        return videoCommonService.preSetDelete(paramVideoNode);
    }


    /**
     * 建立RTC连接
     *
     * @return 返回RTC连接相关信息的DTO结果，包括错误信息或连接参数
     * 此方法根据设备序列号和频道ID来建立RTC连接。首先，它验证设备序列号是否为空，
     * 然后根据序列号查找设备。如果设备不存在，返回错误信息。接下来，根据设备的访问方式，
     * 选择不同的方式来建立连接。对于GB访问方式，它尝试获取GB直播地址；对于其他访问方式，
     * 它尝试获取RTC直播地址。如果获取地址过程中出现异常，将返回异常信息。
     */
    public DtoResult<VlinkerConvergeRtcConnectResp> rtcConn(RtcConnParam connParam) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (connParam.getDeviceId() != null) {
            deviceEntity = deviceService.getById(connParam.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(connParam.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, connParam.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, connParam.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        GetVoiceUrlParam paramVideoNode = new GetVoiceUrlParam();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(connParam.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setChannelXId(one.getNodeXId());
        paramVideoNode.setPlayType(connParam.getPlayType());
        return videoCommonService.getVoiceUrl(paramVideoNode);
    }


    public DtoResult<VlinkerConvergeFlipVideoResp> flipVideo(VlinkerFlipVideoReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        PictureFlipControlParamVideoNode paramVideoNode = new PictureFlipControlParamVideoNode();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(vlinkerReq.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setChannelXId(one.getNodeXId());
        paramVideoNode.setFrameMirror(vlinkerReq.getFrameMirror());
        return videoCommonService.pictureFlipControl(paramVideoNode);
    }

    public DtoResult<VlinkerConvergeFlipVideoResp> getFlipVideo(VlinkerFlipVideoReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        PictureFlipControlParamVideoNode paramVideoNode = new PictureFlipControlParamVideoNode();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(vlinkerReq.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setChannelXId(one.getNodeXId());
        paramVideoNode.setOpsDeviceChannelEntity(one);
        return videoCommonService.getPictureFlipParam(paramVideoNode);
    }

    public DtoResult<ConvergeSdCardCapacityResp> getSdCardInfo(VlinkerSdCardInfoReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        PictureFlipControlParamVideoNode paramVideoNode = new PictureFlipControlParamVideoNode();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(vlinkerReq.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setChannelXId(one.getNodeXId());
        return videoCommonService.getSdCardCapacity(paramVideoNode);
    }

    public DtoResult<CommonChannelAbilityResp> getChannelAbility(ConvDeviceReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        } else if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam paramVideoNode = new VideoNodeBaseParam();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(vlinkerReq.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setChannelXId(one.getNodeXId());
        return videoCommonService.getChannelAbility(paramVideoNode);
    }


    public DtoResult<Void> sdCardInfoFormat(VlinkerSdCardInfoReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        PictureFlipControlParamVideoNode paramVideoNode = new PictureFlipControlParamVideoNode();
        paramVideoNode.setDeviceId(deviceEntity.getId());
        paramVideoNode.setDeviceCode(deviceEntity.getDeviceCode());
        paramVideoNode.setChannelId(vlinkerReq.getChannelId());
        paramVideoNode.setDeviceEntity(deviceEntity);
        paramVideoNode.setOpsDeviceChannelEntity(one);
        paramVideoNode.setChannelXId(one.getNodeXId());
        return videoCommonService.sdCardFormatting(paramVideoNode);
    }


    public DtoResult<CommonGetSideAlgorithmResp> getSideAlgorithmInfo(ConvGetSideAlgorithmReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(vlinkerReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        return videoCommonService.getAlarmParameter(videoNodeBaseParam, vlinkerReq);
    }

    public DtoResult<SaveOrUpdateCruiseTrackResp> saveOrUpdateCruiseTrack(ConvSaveOrUpdateCruiseTrackReq vlinkerReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (vlinkerReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(vlinkerReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(vlinkerReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, vlinkerReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, vlinkerReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(vlinkerReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        return videoCommonService.saveOrUpdateCruiseTrack(videoNodeBaseParam, vlinkerReq);
    }

    public DtoResult<CommonSetSideAlgorithmResp> setSideAlgorithmInfo(ConvSetSideAlgorithmReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }

        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        return videoCommonService.setAlarmParameter(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<CommonGetNowDeviceVersionResp> getNowDeviceVersionInfo(ConvDeviceReq convDeviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (convDeviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(convDeviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(convDeviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, convDeviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        CommonGetNowDeviceVersionResp commonGetNowDeviceVersionResp = new CommonGetNowDeviceVersionResp();
        commonGetNowDeviceVersionResp.setModel(deviceEntity.getModel());
        commonGetNowDeviceVersionResp.setNowVersion(deviceEntity.getVersion());
        if (StringUtil.isNotEmpty(deviceEntity.getModel())) {
            DeviceModelEntity deviceModel = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                    .eq(DeviceModelEntity::getModel, deviceEntity.getModel()));
            if (deviceModel != null) {
                DeviceModelVersionEntity versionEntity = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, deviceModel.getId())
                        .isNotNull(DeviceModelVersionEntity::getNumLong)
                        .isNotNull(DeviceModelVersionEntity::getUrl)
                        .isNotNull(DeviceModelVersionEntity::getFileSize)
                        .orderByDesc(DeviceModelVersionEntity::getNumLong)
                        .orderByDesc(DeviceModelVersionEntity::getId)
                        .last(" limit 1 "));
                if (versionEntity != null) {
                    commonGetNowDeviceVersionResp.setNewVersion(versionEntity.getVersionNum());
                }
            }
        }
        return DtoResult.ok(commonGetNowDeviceVersionResp);
    }

    public DtoResult<CommonGetDeviceVersionListResp> getNowDeviceVersionListInfo(ConvDevicePageReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        List<CommonGetDeviceVersionListResp.DeviceVersionListResp> resList = new ArrayList<>();
        if (StringUtil.isNotEmpty(deviceEntity.getModel())) {
            DeviceModelEntity deviceModel = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                    .eq(DeviceModelEntity::getModel, deviceEntity.getModel()));
            if (deviceModel != null) {
                PageHelper.startPage(deviceReq.getPageNum(), deviceReq.getPageSize());
                List<DeviceModelVersionEntity> list = deviceModelVersionService.list(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, deviceModel.getId())
                        .orderByDesc(DeviceModelVersionEntity::getNumLong)
                        .orderByDesc(DeviceModelVersionEntity::getId));
                PageInfo<DeviceModelVersionEntity> pageInfo = new PageInfo<>(list);
                list.forEach(o -> {
                    resList.add(CommonGetDeviceVersionListResp.DeviceVersionListResp.builder()
                            .updateTime(o.getReleaseDate())
                            .version(o.getVersionNum())
                            .type(1)
                            .remark("主要更新内容：bug修复")
                            .build());
                });
                // **构造新的 PageInfo<DeviceListResp>**
                PageInfo<CommonGetDeviceVersionListResp.DeviceVersionListResp> result = new PageInfo<>(resList);
                result.setTotal(pageInfo.getTotal());
                result.setPageNum(pageInfo.getPageNum());
                result.setPageSize(pageInfo.getPageSize());
                result.setPages(pageInfo.getPages());
                CommonGetDeviceVersionListResp commonGetNowDeviceVersionResp = new CommonGetDeviceVersionListResp();
                commonGetNowDeviceVersionResp.setPageInfo(new BasePageInfoEntity<>(result));
                return DtoResult.ok(commonGetNowDeviceVersionResp);
            }
        }
        return DtoResult.error("未找到版本记录！");
    }

    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        CommonDeviceUpgradeReq req = null;
        if (StringUtil.isNotEmpty(deviceEntity.getModel())) {
            DeviceModelEntity deviceModel = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                    .eq(DeviceModelEntity::getModel, deviceEntity.getModel()));
            if (deviceModel != null) {
                DeviceModelVersionEntity versionEntity = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, deviceModel.getId())
                        .orderByDesc(DeviceModelVersionEntity::getNumLong)
                        .orderByDesc(DeviceModelVersionEntity::getId)
                        .last(" limit 1 "));
                if (versionEntity != null) {
                    if (versionEntity.getVersionNum().equals(deviceEntity.getVersion())) {
                        return DtoResult.error("设备已是最新版本！");
                    }
                    if (StringUtil.isEmpty(versionEntity.getUrl())) {
                        return DtoResult.error("设备升级文件不存在！");
                    }
                    req = new CommonDeviceUpgradeReq();
                    req.setFileUrl(versionEntity.getUrl());
                    req.setFileMd5(versionEntity.getMd5());
                    req.setFileSize(versionEntity.getFileSize());
                }
            }
        }
        if (req == null) {
            return DtoResult.error("设备升级文件不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
//        CommonSetFileReq setFileReq = new CommonSetFileReq();
//        setFileReq.setFileUrl(req.getFileUrl());
//        setFileReq.setFileHash(req.getFileMd5());
//        setFileReq.setFileSize(req.getFileSize());
//        setFileReq.setStoragePrefer(new byte[]{2, 1, 0});
//        setFileReq.setFileTag("deviceUpgrade");
//        DtoResult<Void> voidDtoResult = videoCommonService.setDeviceFile(videoNodeBaseParam, setFileReq);
//        if (voidDtoResult.success()) {
//            return DtoResult.ok();
//        } else {
//            return DtoResult.error(voidDtoResult.getMessage(), voidDtoResult.getError());
//        }
        return videoCommonService.deviceUpgrade(videoNodeBaseParam, req);
    }


    public DtoResult<Void> deviceRestart(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        return videoCommonService.deviceRestart(videoNodeBaseParam);
    }

    public DtoResult<Void> deviceReset(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        return videoCommonService.deviceReset(videoNodeBaseParam);
    }

    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getOsdInfo(videoNodeBaseParam);
    }

    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(ConvCallDeviceByVideoReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        return videoCommonService.callDeviceByVideo(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<CommonGetDormancyResp> getDormancyParameter(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getDormancyParameter(videoNodeBaseParam);
    }

    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(ConvSetOsdInfoReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setOsdInfo(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<CommonSetDormancyResp> setDormancyParameter(ConvSetDormancyParameterReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setDormancyParameter(videoNodeBaseParam, deviceReq);
    }


    /**
     * 通过人闸设备查询人员信息
     *
     * @param req 查询请求，包含设备序列号和分页信息
     * @return 返回查询结果，包含人员信息和分页信息
     * @解释器 该方法通过人闸设备的序列号查询指定设备的人员通行记录。
     * 首先验证请求参数的合法性，然后根据设备序列号查询设备信息。
     * 如果设备不存在，则返回错误信息。接着，构造向量子系统发送的请求，
     * 并解析响应以获取人员信息。最后，将人员信息包装在分页信息中返回。
     */
    public DtoResult<BasePageInfoEntity<SearchPeopleByHumanGateResp>> searchPeopleByHumanGate(SearchPeopleByHumanGateReq req) {
        try {
            // 校验设备序列号是否为空
            String deviceSn = req.getDeviceSn();
            if (StringUtil.isEmpty(deviceSn)) {
                return DtoResult.error("deviceSn不能为空！");
            }
            // 校验每页记录数是否超过限制
            if (req.getPageSize() > 30) {
                return DtoResult.error("pageSize不能超过30！");
            }
            // 根据设备序列号查询设备信息
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceSn)
                    .last("LIMIT 1"), false);
            // 如果设备不存在，则返回错误信息
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }
            // 根据设备ID查询信号节点信息
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }
            // 构造向人闸系统查询人员的请求
            QxSearchPeopleByHumanGateReq qxSearchPeopleByHumanGateReq = new QxSearchPeopleByHumanGateReq();
            qxSearchPeopleByHumanGateReq.setPage(req.getPageNum());
            qxSearchPeopleByHumanGateReq.setPageSize(req.getPageSize());
            qxSearchPeopleByHumanGateReq.setDeviceId(req.getDeviceSn());

            // 发送查询请求并解析响应
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SEARCH_EMPLOYEE, node, qxSearchPeopleByHumanGateReq);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点查询人员异常", responseDto.getMsg());
            }
            String resp = responseDto.getRes();
            QxSearchPeopleByHumanGateResp qxSearchPeopleByHumanGateResp = JSON.parseObject(resp, QxSearchPeopleByHumanGateResp.class);
            // 如果没有人员信息，则返回空结果
            if (null == qxSearchPeopleByHumanGateResp || CollectionUtil.isEmpty(qxSearchPeopleByHumanGateResp.getList())) {
                return DtoResult.ok();
            }
            List<QxSearchPeopleByHumanGateResp.PeopleInfo> peopleInfoList = qxSearchPeopleByHumanGateResp.getList();
            if (CollectionUtil.isEmpty(peopleInfoList)) {
                return DtoResult.ok();
            }
            // 将查询结果转换为业务对象
            List<SearchPeopleByHumanGateResp> list = peopleInfoList.stream().map(t1 -> {
                SearchPeopleByHumanGateResp searchPeopleByHumanGateResp = new SearchPeopleByHumanGateResp();
                searchPeopleByHumanGateResp.setUserId(t1.getUserId());
                searchPeopleByHumanGateResp.setName(t1.getName());
                searchPeopleByHumanGateResp.setBegTime(t1.getBeginTime());
                searchPeopleByHumanGateResp.setEndTime(t1.getEndTime());
                return searchPeopleByHumanGateResp;
            }).collect(Collectors.toList());

            // 构建分页信息
            BasePageInfoEntity<SearchPeopleByHumanGateResp> basePageInfoEntity = new BasePageInfoEntity<>();
            basePageInfoEntity.setPages(qxSearchPeopleByHumanGateResp.getTotalPage());
            basePageInfoEntity.setTotal(qxSearchPeopleByHumanGateResp.getTotal());
            basePageInfoEntity.setCurrent(req.getPageNum());
            basePageInfoEntity.setSize(req.getPageSize());
            basePageInfoEntity.setRecords(list);
            // 返回查询结果
            return DtoResult.ok(basePageInfoEntity);
        } catch (Exception e) {
            // 记录异常信息并返回错误信息
            log.error("{}.错误...msg={}", QxNodeApiEnum.SEARCH_EMPLOYEE.getDes(), e.getMessage(), e);
            return DtoResult.error("通过人闸设备查询人员信息失败！");
        }
    }


    /**
     * 通过人行闸机接口添加人员信息。
     * <p>
     * 该方法实现了将人员信息添加到人行闸机的逻辑。首先，它验证了请求参数的完整性，
     * 然后根据设备序列号查询设备信息，确保设备存在。接下来，它构建了用于与闸机通信的请求对象，
     * 并发送请求以添加人员信息。如果添加成功，方法返回一个表示成功的响应。
     * </p>
     *
     * @param req 添加人员的请求对象，包含设备序列号、用户ID、姓名和人脸URL等信息。
     * @return 如果添加成功，返回一个空的DTO结果；如果添加失败或出现异常，返回包含错误信息的DTO结果。
     */
    public DtoResult<Void> addPeopleByHumanGate(AddPeopleByHumanGateReq req) {
        try {
            // 验证请求参数是否为空
            if (StringUtil.isEmpty(req.getDeviceSn()) || StringUtil.isEmpty(req.getUserId()) || StringUtil.isEmpty(req.getName()) || StringUtil.isEmpty(req.getFaceUrl())) {
                return DtoResult.error("参数不能为空！");
            }

            // 根据设备序列号查询设备信息
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                    .last("LIMIT 1"), false);

            // 检查设备是否存在
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }

            // 根据设备ID获取信号节点信息
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }

            // 构建向闸机发送的添加人员请求对象
            QxAddPeopleByHumanGateReq qxAddPeopleByHumanGateReq = new QxAddPeopleByHumanGateReq();
            qxAddPeopleByHumanGateReq.setDeviceId(req.getDeviceSn());
            qxAddPeopleByHumanGateReq.setUserId(req.getUserId());
            qxAddPeopleByHumanGateReq.setName(req.getName());
            qxAddPeopleByHumanGateReq.setFaceUrl(req.getFaceUrl());
            qxAddPeopleByHumanGateReq.setStartTime(req.getStartTime());
            qxAddPeopleByHumanGateReq.setEndTime(req.getEndTime());

            // 向闸机发送添加人员请求，并处理响应
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.ADD_EMPLOYEE, node, qxAddPeopleByHumanGateReq);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点新增人员异常 ", responseDto.getMsg());
            }
            String resp = responseDto.getRes();
            // 如果请求成功，返回一个空的DTO结果
            return DtoResult.ok();
        } catch (Exception e) {
            // 记录异常信息，并返回包含异常信息的DTO结果
            log.error("{}.错误...msg={}", QxNodeApiEnum.ADD_EMPLOYEE.getDes(), e.getMessage(), e);
            return DtoResult.error("通过人行闸机接口添加人员信息失败");
        }
    }


    /**
     * 通过人行闸机删除人员。
     * <p>
     * 该接口用于处理删除指定设备关联的人员请求。首先验证请求参数是否为空，然后根据设备序列号查询设备信息。
     * 如果设备不存在，则返回错误信息。接着，通过设备ID获取信号节点信息，构造并发送删除人员的请求到对应的节点。
     * 如果请求成功，则返回成功结果；如果发生异常，则记录错误信息并返回错误结果。
     * </p>
     *
     * @param req 删除人员请求对象，包含设备序列号和用户ID。
     * @return 返回处理结果，如果删除成功，则返回成功结果；否则返回错误信息。
     */
    public DtoResult<Void> deletePeopleByHumanGate(DeletePeopleByHumanGateReq req) {
        try {
            // 验证请求参数是否为空
            if (StringUtil.isEmpty(req.getDeviceSn())
                    || StringUtil.isEmpty(req.getUserId())
            ) {
                return DtoResult.error("参数不能为空！");
            }

            // 根据设备序列号查询设备信息
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceSn())
                    .last("LIMIT 1"), false);

            // 检查设备是否存在
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }

            // 根据设备ID获取信号节点信息
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }

            // 构造删除人员请求对象
            QxDeletePeopleByHumanGateReq qxDeletePeopleByHumanGateReq = new QxDeletePeopleByHumanGateReq();
            qxDeletePeopleByHumanGateReq.setDeviceId(req.getDeviceSn());
            qxDeletePeopleByHumanGateReq.setUserId(req.getUserId());

            // 发送删除人员请求到对应的信号节点，并处理响应
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DELETE_EMPLOYEE, node, qxDeletePeopleByHumanGateReq);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点删除人员异常 ", responseDto.getMsg());
            }
            String resp = responseDto.getRes();
            // 如果请求成功，返回成功结果
            return DtoResult.ok();
        } catch (Exception e) {
            // 记录异常信息，并返回错误结果
            log.error("{}.错误...msg={}", QxNodeApiEnum.DELETE_EMPLOYEE.getDes(), e.getMessage(), e);
            return DtoResult.error("通过人行闸机删除人员失败");
        }
    }


    /**
     * 新增车辆信息同步车闸
     *
     * @param req 包含车辆信息和设备ID等必要参数的请求对象
     * @return 返回操作结果，成功则返回一个空的DtoResult，失败则返回包含错误信息的DtoResult
     */
    public DtoResult<Void> addCarInfoToGate(AddCarInfoGateReq req) {
        try {
            // 校验请求参数是否为空
            if (StringUtil.isEmpty(req.getAction())
                    || StringUtil.isEmpty(req.getDeviceId())
                    || StringUtil.isEmpty(req.getLicensePlate())
                    || StringUtil.isEmpty(req.getPlateColor())
                    || StringUtil.isEmpty(req.getPlateType())
                    || StringUtil.isEmpty(req.getListType())
            ) {
                return DtoResult.error("参数不能为空！");
            }

            // 根据设备ID查询设备实体
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceId())
                    .last("LIMIT 1"), false);

            // 设备不存在时返回错误信息
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }

            // 根据设备ID获取对应的信号节点
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }

            // 向车闸发送新增车辆信息的请求并处理响应
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.ADD_CAR_INFO, node, req);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点新增车辆信息异常 ", responseDto.getMsg());
            }
            String resp = responseDto.getRes();

            // 请求成功，输出结果
            log.info("resp={}", resp);
            // 请求成功，返回成功结果
            return DtoResult.ok();
        } catch (Exception e) {
            // 记录请求失败的日志
            log.error("{}.错误...msg={}", QxNodeApiEnum.ADD_CAR_INFO.getDes(), e.getMessage(), e);
            // 返回错误信息
            return DtoResult.error("新增车辆信息同步车闸失败");
        }
    }

    /**
     * 删除车辆信息同步车闸
     *
     * @param req 包含删除车辆信息请求参数的对象，必须包括设备ID、车牌号和车牌颜色
     * @return 返回操作结果，成功则返回一个空的DtoResult，失败则返回包含错误信息的DtoResult
     */
    public DtoResult<Void> deleteCarInfoToGate(DeleteCarInfoGateReq req) {
        try {
            // 校验请求参数是否为空
            if (StringUtil.isEmpty(req.getDeviceId())
                    || StringUtil.isEmpty(req.getLicensePlate())
                    || StringUtil.isEmpty(req.getPlateColor())
            ) {
                return DtoResult.error("参数不能为空！");
            }

            // 根据设备ID查询设备实体
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceId())
                    .last("LIMIT 1"), false);

            // 设备不存在时返回错误信息
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }

            // 根据设备ID获取对应的信号节点
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }

            // 向车闸发送删除车辆信息的请求
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DELETE_CAR_INFO, node, req);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点删除车辆信息异常", responseDto.getMsg());
            }
            String resp = responseDto.getRes();
            // 请求成功，输出结果
            log.info("resp={}", resp);
            // 请求成功，返回成功结果
            return DtoResult.ok();
        } catch (Exception e) {
            // 记录请求发送过程中的异常信息
            log.error("{}.错误...msg={}", QxNodeApiEnum.DELETE_CAR_INFO.getDes(), e.getMessage(), e);
            // 返回异常信息
            return DtoResult.error("删除车辆信息同步车闸");
        }
    }

    /**
     * 查询闸机车辆信息。
     *
     * @param req 包含查询条件的请求对象，包括设备ID、页码和页面大小等信息。
     * @return 返回车辆信息的查询结果，如果查询成功，包含车辆信息列表及其分页信息；如果查询失败，返回错误信息。
     */
    public DtoResult<BasePageInfoEntity<QxSearchCarInfoByGateResp.CarInfo>> searchCarInfoToGate(SearchCarInfoByGateReq req) {
        try {
            // 校验设备ID是否为空
            String deviceSn = req.getDeviceId();
            if (StringUtil.isEmpty(deviceSn)) {
                return DtoResult.error("deviceSn不能为空！");
            }
            // 校验页面大小是否超过限制
            if (req.getPageSize() > 30) {
                return DtoResult.error("pageSize不能超过30！");
            }
            // 查询设备信息
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceSn)
                    .last("LIMIT 1"), false);
            if (deviceEntity == null) {
                return DtoResult.error("设备不存在");
            }
            // 根据设备ID获取信号节点信息
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return DtoResult.error("设备未绑定节点！");
            }
            // 向指定节点发送查询车辆信息的请求
            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SEARCH_CAR_INFO, node, req);
            if (responseDto.getHttpCode() != 200) {
                return DtoResult.error("信令节点查询车辆信息异常", responseDto.getMsg());
            }
            String resp = responseDto.getRes();
            // 解析响应结果
            QxSearchCarInfoByGateResp qxSearchCarInfoByGateResp = JSON.parseObject(resp, QxSearchCarInfoByGateResp.class);
            List<QxSearchCarInfoByGateResp.CarInfo> carInfoList = qxSearchCarInfoByGateResp.getList();
            // 如果没有查询到车辆信息，直接返回
            if (CollectionUtil.isEmpty(carInfoList)) {
                return DtoResult.ok();
            }
            // 组装分页信息和车辆信息列表，返回查询结果
            BasePageInfoEntity<QxSearchCarInfoByGateResp.CarInfo> basePageInfoEntity = new BasePageInfoEntity<>();
            basePageInfoEntity.setPages(qxSearchCarInfoByGateResp.getTotalPage());
            basePageInfoEntity.setTotal(qxSearchCarInfoByGateResp.getTotal());
            basePageInfoEntity.setCurrent(req.getPageNum());
            basePageInfoEntity.setSize(req.getPageSize());
            basePageInfoEntity.setRecords(carInfoList);
            return DtoResult.ok(basePageInfoEntity);
        } catch (Exception e) {
            // 记录查询异常
            log.error("{}.错误...msg={}", QxNodeApiEnum.SEARCH_CAR_INFO.getDes(), e.getMessage(), e);
            return DtoResult.error("查询闸机车辆信息失败！");
        }
    }

    public DeviceCapacityVo getDeviceCapacity(String deviceCode) {
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode, deviceCode));
        if (Objects.isNull(deviceEntity)) {
            return new DeviceCapacityVo();
        }
        DeviceCapacityVo.DeviceCapacityVoBuilder builder = DeviceCapacityVo.builder();
        DeviceModelEntity deviceModelEntity = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(DeviceModelEntity::getModel, deviceEntity.getModel()), false);
        if (null != deviceModelEntity) {
            // 查询设备能力
            DeviceModelVersionEntity modelVersion = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                    .eq(DeviceModelVersionEntity::getModelId, deviceModelEntity.getId())
                    .eq(DeviceModelVersionEntity::getVersionNum, deviceEntity.getVersion()), false
            );
            if (Objects.nonNull(modelVersion)) {
                builder.deviceCapacity(modelVersion.getDeviceCapacity());
                builder.deviceAlarm(modelVersion.getDeviceAlarm());
                builder.faultAlarm(modelVersion.getFaultAlarm());
                builder.gpsAlarm(modelVersion.getGpsAlarm());
                builder.videoAlarm(modelVersion.getVideoAlarm());
            }
        }
        if (!Objects.equals(deviceEntity.getAccessWay(), AccessWayType.SDSDK.getType())) {
            DeviceModelVersionEntity version = new DeviceModelVersionEntity();
            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                version.addDeviceCapacity(byField);
            });
            builder.deviceCapacity(version.getDeviceCapacity()).build();
        }
        return builder.build();
    }

    public DtoResult<List<DeviceInfoEntity>> syncDeviceAndChannelName
            (List<DeviceInfoEntity> deviceInfoEntityList) {
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            return DtoResult.ok();
        }
        Set<String> deviceCodeSet = new HashSet<>();
        Set<String> channelCodeSet = new HashSet<>();
        for (DeviceInfoEntity deviceInfoEntity : deviceInfoEntityList) {
            if (StringUtil.isNotEmpty(deviceInfoEntity.getTripartiteSn())) {
                deviceCodeSet.add(deviceInfoEntity.getTripartiteSn());
            }
            if (StringUtil.isNotEmpty(deviceInfoEntity.getChannelId())) {
                channelCodeSet.add(deviceInfoEntity.getChannelId());
            }
        }
        List<DeviceEntity> deviceEntityList = new ArrayList<>();
        List<OpsDeviceChannelEntity> opsDeviceChannelEntityList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceCodeSet)) {
            deviceEntityList = deviceService.list(new LambdaQueryWrapper<DeviceEntity>()
                    .in(DeviceEntity::getDeviceCode, deviceCodeSet));
        }
        if (CollectionUtil.isNotEmpty(channelCodeSet)) {
            opsDeviceChannelEntityList = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                    .in(OpsDeviceChannelEntity::getChannelId, channelCodeSet));
        }
        List<DeviceInfoEntity> resultDeviceInfoEntityList = new ArrayList<>();
        for (DeviceInfoEntity deviceInfoEntity : deviceInfoEntityList) {
            DeviceInfoEntity resultDeviceInfoEntity = new DeviceInfoEntity();
            resultDeviceInfoEntity.setId(deviceInfoEntity.getId());
            resultDeviceInfoEntity.setTripartiteSn(deviceInfoEntity.getTripartiteSn());
            resultDeviceInfoEntity.setChannelId(deviceInfoEntity.getChannelId());
            boolean flag = false;

            Optional<DeviceEntity> deviceOptional = deviceEntityList.stream().filter(deviceEntity -> deviceInfoEntity.getTripartiteSn().equals(deviceEntity.getDeviceCode())).findFirst();
            if (deviceOptional.isPresent()) {
                flag = true;
                DeviceEntity deviceEntity = deviceOptional.get();
                resultDeviceInfoEntity.setDeviceName(deviceEntity.getName());
            }
            Optional<OpsDeviceChannelEntity> channelOptional = opsDeviceChannelEntityList.stream().filter(channelEntity -> deviceInfoEntity.getChannelId().equals(channelEntity.getChannelId())).findFirst();
            if (channelOptional.isPresent()) {
                flag = true;
                OpsDeviceChannelEntity opsDeviceChannelEntity = channelOptional.get();
                resultDeviceInfoEntity.setChannelName(opsDeviceChannelEntity.getChannelName());
            }
            if (flag) {
                resultDeviceInfoEntityList.add(resultDeviceInfoEntity);
            }
        }

        return DtoResult.ok(resultDeviceInfoEntityList);
    }

    public ConvDeviceAndChannelDTO getDeviceChannelList(DeviceEntity deviceEntity) {
        ConvDeviceAndChannelDTO convDeviceAndChannelDTO = new ConvDeviceAndChannelDTO();
        String sn = deviceEntity.getSn();
        if (StringUtil.isEmpty(sn)) {
            return convDeviceAndChannelDTO;
        }
        DeviceEntity localDeviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getSn, sn), false);
        if (Objects.isNull(localDeviceEntity)) {
            return convDeviceAndChannelDTO;
        }
        convDeviceAndChannelDTO.setDeviceEntity(localDeviceEntity);
        convDeviceAndChannelDTO.setOpsDeviceChannelEntityList(opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, localDeviceEntity.getSn())));
        return convDeviceAndChannelDTO;
    }

    @Resource
    private Map<String, DeviceSdkAccessService> deviceSdkAccessServiceMap;

    public DtoResult<ThirdPartyPlatformsQuerySupportPrecisePtzResp> querySupportPrecisePtz(ThirdPartyPlatformsQuerySupportPrecisePtzReq req) {
        ThirdPartyPlatformsQuerySupportPrecisePtzResp resp = new ThirdPartyPlatformsQuerySupportPrecisePtzResp();
        if (CollectionUtil.isEmpty(req.getDevice())) {
            return DtoResult.ok(resp);
        }
        List<ThirdPartyPlatformsQuerySupportPrecisePtzResp.QuerySupportPrecisePtzResp> querySupportPrecisePtzRespList = new ArrayList<>();

        req.getDevice().forEach(device -> {
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, device.getDeviceId()));
            ThirdPartyPlatformsQuerySupportPrecisePtzResp.QuerySupportPrecisePtzResp querySupportPrecisePtzResp = new ThirdPartyPlatformsQuerySupportPrecisePtzResp.QuerySupportPrecisePtzResp();
            querySupportPrecisePtzResp.setDeviceId(device.getDeviceId());
            if (deviceEntity != null) {
                // sdk接入开启
                SdkAccessType sdkAccessType = SdkAccessType.getByCode(deviceEntity.getSdkAccess());
                if (sdkAccessType == null) {
                    log.error("sdkAccessType is null: 没有找到对应的sdk信息？");
                    return;
                }
                DeviceSdkAccessService deviceSdkAccessService = deviceSdkAccessServiceMap.get(sdkAccessType.getClazz());
                if (deviceSdkAccessService == null) {
                    log.error("deviceSdkAccessService is null: 没有找到对应的sdk信息？");
                    return;
                }
                boolean onlineByDeviceCode = deviceSdkAccessService.sdkOnline(deviceEntity);
                querySupportPrecisePtzResp.setSupport(onlineByDeviceCode ? 1 : 2);
            } else {
                querySupportPrecisePtzResp.setSupport(2);
            }
            log.info("设备编号：{}，设备是否在线：{} ,deviceEntity:{}", device.getDeviceId(), querySupportPrecisePtzResp.getSupport(), deviceEntity == null);
            querySupportPrecisePtzRespList.add(querySupportPrecisePtzResp);
        });
        resp.setQuerySupportPrecisePtzDtoList(querySupportPrecisePtzRespList);
        return DtoResult.ok(resp);
    }

    public Result getDeviceOsd(Long deviceId, String channelId) {
        // 根据设备ID查询设备实体
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        // 如果未提供通道ID，尝试获取设备的主通道ID
        if (StringUtil.isEmpty(channelId)) {
            return Result.error("通道必传");
        }
        //
        // 根据设备ID查询信号节点实体
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
        if (node == null) {
            return Result.error("设备未绑定节点！");
        }
        // 请求获取设备的OSD配置
        GetDeviceConfigReq getChannelsReq = new GetDeviceConfigReq();
        getChannelsReq.setDeviceId(deviceEntity.getDeviceCode());
        getChannelsReq.setChannelId(channelId);
        getChannelsReq.setConfigCmd("OSDConfig");
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_DEVICE_CONFIG, node, getChannelsReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.GET_DEVICE_CONFIG.getDes() + "失败");
        }
        DeviceConfigResp resp = JSON.parseObject(responseDto.getRes(), DeviceConfigResp.class);
        // 返回设备的OSD配置
        return Result.ok(resp.getOsdConfig());
    }


    public DtoResult<VlinkerConvergeFlipVideoResp> getDeviceFrameMirror(Long deviceId, String channelId) {
        // 根据设备ID查询设备实体
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 如果未提供通道ID，尝试获取设备的主通道ID
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, channelId), false);
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        PictureFlipControlParamVideoNode cruiseTrackNodeParam = new PictureFlipControlParamVideoNode();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(channelId);
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getPictureFlipParam(cruiseTrackNodeParam);
    }

    public Result updateDeviceOsd(Long deviceId, String channelId, DeviceConfigResp.OsdConfig osdConfig) {
        // 根据设备ID获取设备实体
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        // 根据设备ID获取信号节点实体
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
        if (node == null) {
            return Result.error("设备未绑定节点！");
        }
        // 创建用于更新设备配置的响应对象
        DeviceConfigResp resp = new DeviceConfigResp();
        // 设置OSD配置到响应对象
        resp.setOsdConfig(osdConfig);
        // 调用服务更新设备配置
        SetDeviceConfigReq setDeviceConfigReq = new SetDeviceConfigReq();
        setDeviceConfigReq.setDeviceId(deviceEntity.getDeviceCode());
        setDeviceConfigReq.setChannelId(channelId);
        setDeviceConfigReq.setVideoParamOpt(resp.getVideoParamOpt());
        setDeviceConfigReq.setVideoParamAttribute(resp.getVideoParamAttribute());
        setDeviceConfigReq.setVideoRecordPlan(resp.getVideoRecordPlan());
        setDeviceConfigReq.setVideoRecordPlanConf(resp.getVideoRecordPlanConf());
        setDeviceConfigReq.setFrameMirror(resp.getFrameMirror());
        setDeviceConfigReq.setOsdConfig(resp.getOsdConfig());
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.UPDATE_DEVICE_CONFIG, node, setDeviceConfigReq);
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(QxNodeApiEnum.UPDATE_DEVICE_CONFIG.getDes() + "失败");
        }
        // 返回操作成功的结果
        return Result.ok();
    }

    public Result formatSdCard(Long deviceId, Integer formatSdCard) {
        // 根据设备ID查询设备实体信息
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        // 根据设备ID获取对应的信号节点实体
        SignalNodeEntity node = signalNodeService.getSignalNodeByDeviceId(deviceEntity.getId());
        // 调用远程服务，获取SD卡格式化信息
        FormatSdCardReq formatSdCardReq = new FormatSdCardReq();
        formatSdCardReq.setDeviceId(deviceEntity.getDeviceCode());
        formatSdCardReq.setFormatSdCard(formatSdCard);
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_SD_CARD_INFO, node, formatSdCardReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.GET_SD_CARD_INFO.getDes() + "失败");
        }
        // 返回SD卡格式化信息
        return Result.ok();
    }

    public Result getSdCardInfo(Long deviceId) {
        // 通过设备ID查询设备实体信息
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在，如果不存在则返回错误信息
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        // 根据设备ID查询信号节点信息
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
        if (node == null) {
            return Result.error("设备未绑定节点！");
        }
        // 调用QXNodeReqService获取SD卡信息，传入信号节点、设备序列号和通道ID
        SDCardInfoReq getChannelsReq = new SDCardInfoReq();
        getChannelsReq.setDeviceId(deviceEntity.getDeviceCode());
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_SD_CARD_INFO, node, getChannelsReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.GET_SD_CARD_INFO.getDes() + "失败");
        }
        SDCardInfoResp resp = JSON.parseObject(responseDto.getRes(), SDCardInfoResp.class);
        // 返回获取到的SD卡信息
        return Result.ok(resp);
    }


    public DtoResult<VlinkerConvergeGetPtzResp> getPtzPrecise(Long deviceId, String channelId) {
        // 根据设备ID获取设备实体
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 如果未提供通道ID，尝试获取设备的主通道ID
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, channelId));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(deviceEntity.getDeviceCode());
        param.setChannelId(channelId);
        param.setDeviceEntity(deviceEntity);
        param.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getPtz(param);
    }


    public Result subscribeAlarm(SubscribeAlarmDto subscribeAlarmDto) {
        // 遍历设备ID列表，对每个设备进行订阅操作
        Arrays.stream(subscribeAlarmDto.getDeviceBean()).forEach(deviceBean -> {
            // 根据设备ID查询设备实体
            DeviceEntity deviceEntity = deviceService.getById(deviceBean.getDeviceId());
            // 检查设备是否存在，如果不存在，则跳过当前设备的订阅操作
            if (deviceEntity == null) {
                return;
            }
            // 从通道信息结果中提取通道ID
            String channelId = deviceBean.getChannelId();
            // 根据设备ID获取对应的信号节点实体
            SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
            if (node == null) {
                return;
            }
            // 调用远程服务，订阅报警信息
            if (subscribeAlarmDto.getDeviceAlarm() != null && subscribeAlarmDto.getDeviceAlarm().length > 0) {
                SubscribeAlarmReq subscribeAlarmReq = new SubscribeAlarmReq();
                subscribeAlarmReq.setDeviceId(deviceEntity.getDeviceCode());
                subscribeAlarmReq.setChannelId(channelId);
                subscribeAlarmReq.setAlarmMethod("2");
                subscribeAlarmReq.setSet(true);
                ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SUBSCRIBE_ALARM, node, subscribeAlarmReq);
                if (responseDto.getHttpCode() != 200) {
                    throw new BizRuntimeException(QxNodeApiEnum.SUBSCRIBE_ALARM.getDes() + "失败");
                }
                deviceEntity.setDeviceAlarm(Arrays.stream(subscribeAlarmDto.getDeviceAlarm()).map(String::valueOf).collect(Collectors.joining(",")));
            }
            if (subscribeAlarmDto.getGpsAlarm() != null && subscribeAlarmDto.getGpsAlarm().length > 0) {
                SubscribeAlarmReq subscribeAlarmReq = new SubscribeAlarmReq();
                subscribeAlarmReq.setDeviceId(deviceEntity.getDeviceCode());
                subscribeAlarmReq.setChannelId(channelId);
                subscribeAlarmReq.setAlarmMethod("4");
                subscribeAlarmReq.setSet(true);
                ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SUBSCRIBE_ALARM, node, subscribeAlarmReq);
                if (responseDto.getHttpCode() != 200) {
                    throw new BizRuntimeException(QxNodeApiEnum.SUBSCRIBE_ALARM.getDes() + "失败");
                }
                deviceEntity.setGpsAlarm(Arrays.stream(subscribeAlarmDto.getGpsAlarm()).map(String::valueOf).collect(Collectors.joining(",")));
            }
            if (subscribeAlarmDto.getFaultAlarm() != null && subscribeAlarmDto.getFaultAlarm().length > 0) {
                Arrays.stream(subscribeAlarmDto.getFaultAlarm()).forEach(alarmType -> {
                    SubscribeAlarmReq subscribeAlarmReq = new SubscribeAlarmReq();
                    subscribeAlarmReq.setDeviceId(deviceEntity.getDeviceCode());
                    subscribeAlarmReq.setChannelId(channelId);
                    subscribeAlarmReq.setAlarmMethod("6");
                    subscribeAlarmReq.setAlarmType(String.valueOf(alarmType));
                    subscribeAlarmReq.setSet(true);
                    ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SUBSCRIBE_ALARM, node, subscribeAlarmReq);
                    if (responseDto.getHttpCode() != 200) {
                        throw new BizRuntimeException(QxNodeApiEnum.SUBSCRIBE_ALARM.getDes() + "失败");
                    }
                });
                deviceEntity.setFaultAlarm(Arrays.stream(subscribeAlarmDto.getFaultAlarm()).map(String::valueOf).collect(Collectors.joining(",")));
            }
            if (subscribeAlarmDto.getVideoAlarm() != null && subscribeAlarmDto.getVideoAlarm().length > 0) {
                Arrays.stream(subscribeAlarmDto.getVideoAlarm()).forEach(alarmType -> {
                    SubscribeAlarmReq subscribeAlarmReq = new SubscribeAlarmReq();
                    subscribeAlarmReq.setDeviceId(deviceEntity.getDeviceCode());
                    subscribeAlarmReq.setChannelId(channelId);
                    subscribeAlarmReq.setAlarmMethod("5");
                    subscribeAlarmReq.setAlarmType(String.valueOf(alarmType));
                    subscribeAlarmReq.setSet(true);
                    ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SUBSCRIBE_ALARM, node, subscribeAlarmReq);
                    if (responseDto.getHttpCode() != 200) {
                        throw new BizRuntimeException(QxNodeApiEnum.SUBSCRIBE_ALARM.getDes() + "失败");
                    }
                });
                deviceEntity.setVideoAlarm(Arrays.stream(subscribeAlarmDto.getVideoAlarm()).map(String::valueOf).collect(Collectors.joining(",")));
            }
            deviceService.updateById(deviceEntity);
        });
        // 返回操作成功的结果
        return Result.ok();
    }

    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrackList(VlinkerCruiseTrackReq req) {

        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            // 根据设备SN查询设备信息
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }

        CruiseTrackNodeParam cruiseTrackNodeParam = new CruiseTrackNodeParam();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(req.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getCruiseTrackList(cruiseTrackNodeParam);
    }

    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrack(VlinkerCruiseTrackReq req) {

        DeviceEntity deviceEntity = null;
        if (req.getDeviceId() != null) {
            deviceEntity = deviceService.getById(req.getDeviceId());
        } else if (StringUtil.isNotEmpty(req.getDeviceCode())) {
            // 根据设备SN查询设备信息
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, req.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, req.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }

        CruiseTrackNodeParam cruiseTrackNodeParam = new CruiseTrackNodeParam();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(req.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setNumber(req.getNumber());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.getCruiseTrack(cruiseTrackNodeParam);
    }

    public DtoResult<Void> startCruiseTrack(VlinkerCruiseTrackReq ctrlCruiseTrackDto) {
        // 获取设备ID和通道ID
        Long deviceId = ctrlCruiseTrackDto.getDeviceId();
        String channelId = ctrlCruiseTrackDto.getChannelId();


        DeviceEntity deviceEntity = null;
        if (ctrlCruiseTrackDto.getDeviceId() != null) {
            deviceEntity = deviceService.getById(ctrlCruiseTrackDto.getDeviceId());
        } else if (StringUtil.isNotEmpty(ctrlCruiseTrackDto.getDeviceCode())) {
            // 根据设备SN查询设备信息
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, ctrlCruiseTrackDto.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }


        // 如果未指定通道ID，尝试获取设备的主通道
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }


        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, ctrlCruiseTrackDto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        CruiseTrackNodeParam cruiseTrackNodeParam = new CruiseTrackNodeParam();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(ctrlCruiseTrackDto.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setNumber(ctrlCruiseTrackDto.getNumber());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.startCruiseTrack(cruiseTrackNodeParam);
    }

    public DtoResult<Void> stopCruiseTrack(VlinkerCruiseTrackReq ctrlCruiseTrackDto) {
        // 获取设备ID和通道ID from DTO
        String channelId = ctrlCruiseTrackDto.getChannelId();

        DeviceEntity deviceEntity = null;
        if (ctrlCruiseTrackDto.getDeviceId() != null) {
            deviceEntity = deviceService.getById(ctrlCruiseTrackDto.getDeviceId());
        } else if (StringUtil.isNotEmpty(ctrlCruiseTrackDto.getDeviceCode())) {
            // 根据设备SN查询设备信息
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, ctrlCruiseTrackDto.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }

        // 如果未提供通道ID，尝试获取设备的主通道
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, ctrlCruiseTrackDto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }

        CruiseTrackNodeParam cruiseTrackNodeParam = new CruiseTrackNodeParam();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(ctrlCruiseTrackDto.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setNumber(ctrlCruiseTrackDto.getNumber());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.stopCruiseTrack(cruiseTrackNodeParam);
    }


    public DtoResult<Void> delCruiseTrack(VlinkerCruiseTrackReq ctrlCruiseTrackDto) {
        // 获取设备ID和通道ID
        Long deviceId = ctrlCruiseTrackDto.getDeviceId();
        String channelId = ctrlCruiseTrackDto.getChannelId();

        DeviceEntity deviceEntity = null;
        if (ctrlCruiseTrackDto.getDeviceId() != null) {
            deviceEntity = deviceService.getById(ctrlCruiseTrackDto.getDeviceId());
        } else if (StringUtil.isNotEmpty(ctrlCruiseTrackDto.getDeviceCode())) {
            // 根据设备SN查询设备信息
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, ctrlCruiseTrackDto.getDeviceCode()), false);
        }
        // 如果设备不存在，则返回错误信息
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }

        // 如果未指定通道ID，尝试获取设备的主通道
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, ctrlCruiseTrackDto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }


        CruiseTrackNodeParam cruiseTrackNodeParam = new CruiseTrackNodeParam();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(ctrlCruiseTrackDto.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setNumber(ctrlCruiseTrackDto.getNumber());
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        return videoCommonService.delCruiseTrack(cruiseTrackNodeParam);
    }


    public DtoResult<Void> ctrlPtzPrecise(CtrlPtzPreciseDto ctrlPtzPreciseDto) {
        // 获取设备ID和通道ID from DTO
        Long deviceId = ctrlPtzPreciseDto.getDeviceId();
        String channelId = ctrlPtzPreciseDto.getChannelId();
        // 根据设备ID查询设备实体信息
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        // 检查设备是否存在
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        // 如果通道ID为空，尝试获取主通道ID
        if (StringUtil.isEmpty(channelId)) {
            return DtoResult.error("通道必传");
        }

        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, ctrlPtzPreciseDto.getChannelId()));
        if (opsDeviceChannelEntity == null) {
            return DtoResult.error("通道不存在");
        }
        SetPTZParamVideoNode cruiseTrackNodeParam = new SetPTZParamVideoNode();
        cruiseTrackNodeParam.setDeviceId(deviceEntity.getId());
        cruiseTrackNodeParam.setDeviceCode(deviceEntity.getDeviceCode());
        cruiseTrackNodeParam.setChannelId(ctrlPtzPreciseDto.getChannelId());
        cruiseTrackNodeParam.setDeviceEntity(deviceEntity);
        cruiseTrackNodeParam.setOpsDeviceChannelEntity(opsDeviceChannelEntity);
        cruiseTrackNodeParam.setChannelXId(deviceEntity.getNodeXId());
        cruiseTrackNodeParam.setPan(ctrlPtzPreciseDto.getP());
        cruiseTrackNodeParam.setTilt(ctrlPtzPreciseDto.getT());
        cruiseTrackNodeParam.setZoom(ctrlPtzPreciseDto.getZ());
        return videoCommonService.setPtz(cruiseTrackNodeParam);

    }

    public DeviceModelVersionVo getDeviceModelAndVersion(String deviceCode) {
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode, deviceCode));
        if (Objects.isNull(deviceEntity)) {
            return new DeviceModelVersionVo();
        }
        DeviceModelEntity deviceModelEntity = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(DeviceModelEntity::getModel, deviceEntity.getModel()), false);
        //最新的版本
        DeviceModelVersionEntity versionServiceOne = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                .eq(DeviceModelVersionEntity::getModelId, deviceModelEntity.getId())
                .orderByDesc(DeviceModelVersionEntity::getCreateTime)
                .last("limit 1"));
        DeviceModelVersionVo.DeviceModelVersionVoBuilder builder = DeviceModelVersionVo.builder();
        if (null != deviceModelEntity) {
            builder.model(deviceModelEntity.getModel()).versionNow(deviceEntity.getVersion()).versionLatest(ObjectUtil.isNull(versionServiceOne) ? "" : versionServiceOne.getVersionNum());
        }
        return builder.build();
    }


    public DtoResult<Void> setDualCameraLinkage(ConvSetDualCameraLinkageReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setDualCameraLinkage(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<Void> setSoundAndLightShock(ConvSetSoundAndLightShockReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setSoundAndLightShock(videoNodeBaseParam, deviceReq);
    }


    public DtoResult<CommonGetSoundAndLightShockReq> getSoundAndLightShock(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getSoundAndLightShock(videoNodeBaseParam);
    }

    public DtoResult<Void> setVolumeCommand(ConvSetVolumeCommandReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setVolumeCommand(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<CommonGetVolumeCommandResp> getVolumeCommand(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getVolumeCommand(videoNodeBaseParam);
    }

    public DtoResult<Void> oneClickPatrol(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.oneClickPatrol(videoNodeBaseParam);
    }

    public DtoResult<Void> setHumanoidMarkers(ConvSetHumanoidMarkersReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setHumanoidMarkers(videoNodeBaseParam, deviceReq);
    }


    public DtoResult<CommonGetHumanoidMarkersReq> getHumanoidMarkers(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getHumanoidMarkers(videoNodeBaseParam);
    }

    public DtoResult<Void> gimbalCalibration(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.gimbalCalibration(videoNodeBaseParam);
    }

    public DtoResult<Void> setDevicRelativeXyz(ConvSetDevicRelativeXyzReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (deviceEntity == null && StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        // 这个地方 不知道什么好的方式
        deviceReq.setChannelNum(one.getChannelName().contains("可见光") ? 0 : 1);
        return videoCommonService.setDevicRelativeXyz(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<Void> voiceStatusNotifyReq(ConvVoiceStatusNotifyReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (deviceEntity == null && StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.notifyVoiceStatus(videoNodeBaseParam, deviceReq);
    }

    public DtoResult<CommonGetScreenPropertiesResp> getScreenInfo(ConvDeviceReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (deviceEntity == null && StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.getScreenInfo(videoNodeBaseParam);
    }

    public DtoResult<Void> setScreenInfo(ConvSetScreenPropertiesReq deviceReq) {
        // 根据设备序列号查询设备信息
        DeviceEntity deviceEntity = null;
        if (deviceReq.getDeviceId() != null) {
            deviceEntity = deviceService.getById(deviceReq.getDeviceId());
        }
        // 验证设备序列号是否为空
        if (deviceEntity == null && StringUtil.isNotEmpty(deviceReq.getDeviceCode())) {
            deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, deviceReq.getDeviceCode()), false);
        }
        if (deviceEntity == null) {
            return DtoResult.error("设备不存在");
        }
        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getChannelId, deviceReq.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("通道不存在！");
        }
        VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
        videoNodeBaseParam.setDeviceId(deviceEntity.getId());
        videoNodeBaseParam.setDeviceCode(deviceEntity.getDeviceCode());
        videoNodeBaseParam.setChannelId(deviceReq.getChannelId());
        videoNodeBaseParam.setDeviceEntity(deviceEntity);
        videoNodeBaseParam.setChannelXId(one.getNodeXId());
        videoNodeBaseParam.setOpsDeviceChannelEntity(one);
        return videoCommonService.setScreenInfo(videoNodeBaseParam, deviceReq);
    }
}
