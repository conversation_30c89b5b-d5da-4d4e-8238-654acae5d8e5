package com.saida.services.system.video.service;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.deviceApi.req.ConvSaveOrUpdateCruiseTrackReq;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.enums.SdkAccessType;
import com.saida.services.converge.qxNode.resp.ChannelRecordTimeLine;
import com.saida.services.converge.qxNode.resp.ChannelRecordUrlResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeLightingArgResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeSdCardCapacityResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeVideoResp;
import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
import com.saida.services.deviceApi.req.*;
import com.saida.services.deviceApi.resp.*;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.resp.*;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.video.deviceSdkAccessService.DeviceSdkAccessService;
import com.saida.services.system.video.param.*;
import com.saida.services.system.video.service.impl.v1.VideoNodeV1StreamServiceImpl;
import com.saida.services.system.video.service.impl.v2.VideoNodeV2StreamServiceImpl;
import com.saida.services.system.video.vo.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 视频通用服务类
 * 提供视频设备的统一操作接口，根据设备版本和协议类型自动选择合适的服务实现
 */
@Service
public class VideoCommonService {

    @Resource
    private VideoNodeV1StreamServiceImpl videoNodeV1StreamService;

    @Resource
    private VideoNodeV2StreamServiceImpl videoNodeV2StreamService;


    @Resource
    private VideoSignalingServiceFactory videoSignalingServiceFactory;


    /**
     * 根据设备版本获取流媒体服务类型
     * @param device 设备实体
     * @return 对应的流媒体服务实现
     */
    private VideoStreamService getAccessWayTypeByStream(DeviceEntity device) {
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        if (device.getNodeVersion() == null || device.getNodeVersion() == 1) {
            return videoNodeV1StreamService;
        }
        return videoNodeV2StreamService;
    }

    /**
     * 获取信令操作的服务类型（非流媒体操作）
     * 当NodeVersion为2时，根据设备接入协议判断处理方式
     */
    private VideoSignalingService getAccessWayTypeForSignaling(DeviceEntity device) {
        return videoSignalingServiceFactory.getSignalingService(device);
    }

    /**
     * 获取播放服务类型（支持利旧播放）
     * @param device 设备实体
     * @param channelXId 通道XID，如果不为空说明已经利旧，可以通过新节点播放
     * @return 对应的流媒体服务实现
     */
    private VideoStreamService getAccessWayTypePlay(DeviceEntity device, Integer channelXId) {
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        // 和上面的区别就是如果有xid 说明已经利旧了  可以通过新节点播放了
        if (channelXId != null) {
            if (StringUtil.isNotEmpty(device.getUtilizeNode())) {
                device.setNodeId(device.getUtilizeNode());
            }
            return videoNodeV2StreamService;
        }
        if (device.getNodeVersion() == null || device.getNodeVersion() == 1) {
            return videoNodeV1StreamService;
        }
        return videoNodeV2StreamService;
    }


    @Resource
    private Map<String, DeviceSdkAccessService> deviceSdkAccessServiceMap;


    /**
     * 获取SDK接入服务类型
     * @param device 设备实体
     * @return SDK接入服务实现，如果未配置SDK接入则返回null
     */
    private DeviceSdkAccessService getAccessWayTypeSdk(DeviceEntity device) {
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        if (device.getSdkAccess() == null || device.getSdkAccess() == 0) {
            return null;
        }
        // sdk接入关闭 关闭的类型是oldSdkAccess
        SdkAccessType sdkAccessType = SdkAccessType.getByCode(device.getSdkAccess());
        if (sdkAccessType == null) {
            return null;
        }
        return deviceSdkAccessServiceMap.get(sdkAccessType.getClazz());
    }

    /**
     * 添加设备到视频系统
     */
    public DtoResult<VlinkerConvergeAddDeviceResp> addDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        return getAccessWayTypeByStream(entity).addDevice(entity, signalNode);
    }

    /**
     * 从视频系统删除设备
     */
    public DtoResult<VlinkerConvergeAddDeviceResp> delDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        return getAccessWayTypeByStream(entity).delDevice(entity, signalNode);
    }

    /**
     * 获取设备直播流URL
     */
    public DtoResult<StreamUrlDto> getStreamUrl(GetStreamUrlParamVideoNode param) {
        return getAccessWayTypePlay(param.getDeviceEntity(), param.getChannelXId()).getStreamUrl(param);
    }

    /**
     * 停止设备直播
     */
    public DtoResult<Void> stopLive(VideoNodeBaseParam param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).stopLive(param);
    }

    /**
     * 获取设备快照
     */
    public DtoResult<VlinkerConvergeSnapshotResp> getSnapshot(GetSnapshotParam param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).getSnapshot(param);
    }

    /**
     * 获取语音通话URL
     */
    public DtoResult<VlinkerConvergeRtcConnectResp> getVoiceUrl(GetVoiceUrlParam param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).getVoiceUrl(param);
    }

    /**
     * 获取设备云端录像月份列表
     */
    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonthsByCloud(GetChannelRecordMonthsParamVideoNode param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).getChannelRecordMonthsByCloud(param);
    }

    /**
     * 获取设备卡存录像月份列表
     */
    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonthsByLocal(GetChannelRecordMonthsParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getChannelRecordMonthsByLocal(param);
    }

    /**
     * 获取设备录像时间轴
     */
    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLineByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).getChannelRecordTimeLineByCloud(param);
    }

    /**
     * 获取设备录像时间轴
     */
    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLineByLocal(GetChannelRecordTimeLineParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getChannelRecordTimeLineByLocal(param);
    }

    /**
     * 获取云录像播放URL
     */
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).getChannelRecordUrlByCloud(param);
    }

    /**
     * 获取本地录像播放URL
     */
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByLocal(GetChannelRecordTimeLineParamVideoNode param) {
        return getAccessWayTypePlay(param.getDeviceEntity(), param.getChannelXId()).getChannelRecordUrlByLocal(param);
    }

    /**
     * 控制录像回放
     */
    public DtoResult<Void> controlPlayback(ControlPlaybackParamVideoNode param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).controlPlayback(param);
    }

    /**
     * 下载录像回放
     */
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackParamVideoNode param) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).downloadPlayback(param);
    }


    /**
     * 获取设备预置位列表
     */
    public DtoResult<List<VlinkerConvergePreSetListResp>> preSetList(VideoNodeBaseParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.preSetList(param);
    }

    /**
     * 设置设备预置位
     */
    public DtoResult<Void> preSet(PreSetParamVideoNode param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.preSet(param);
    }

    /**
     * 获取PTZ参数
     */
    public DtoResult<VlinkerConvergeGetPtzResp> getPtz(VideoNodeBaseParam param) {
        DeviceSdkAccessService accessWayTypeSdk = getAccessWayTypeSdk(param.getDeviceEntity());
        if (accessWayTypeSdk != null) {
            return accessWayTypeSdk.getPtz(param);
        }
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.getPtz(param);
    }

    /**
     * 设置PTZ参数
     */
    public DtoResult<Void> setPtz(SetPTZParamVideoNode param) {
        DeviceSdkAccessService accessWayTypeSdk = getAccessWayTypeSdk(param.getDeviceEntity());
        if (accessWayTypeSdk != null) {
            return accessWayTypeSdk.setPtz(param);
        }
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.setPtz(param);
    }

    /**
     * 设置设备归位位置
     */
    public DtoResult<VlinkerConvergeHomePositionResp> setHomePosition(CtrlHomePositionReq param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.setHomePosition(param);
    }

    /**
     * 获取设备归位位置
     */
    public DtoResult<VlinkerConvergeHomePositionResp> getHomePosition(CtrlHomePositionReq param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.getHomePosition(param);
    }


    /**
     * 跳转到指定预置位
     */
    public DtoResult<Void> preSetJump(PreSetParamVideoNode param) {
        DeviceSdkAccessService accessWayTypeSdk = getAccessWayTypeSdk(param.getDeviceEntity());
        if (accessWayTypeSdk != null && accessWayTypeSdk.isSupportPreSetJump()) {
            return accessWayTypeSdk.preSetJump(param);
        }
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.preSetJump(param);
    }

    /**
     * 删除指定预置位
     */
    public DtoResult<Void> preSetDelete(PreSetParamVideoNode param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.preSetDelete(param);
    }

    /**
     * 云台控制命令
     */
    public DtoResult<Void> ptzCmd(PtzCmdParamVideoNode param) {
        DeviceSdkAccessService accessWayTypeSdk = getAccessWayTypeSdk(param.getDeviceEntity());
        if (accessWayTypeSdk != null) {
            return accessWayTypeSdk.ptzCmd(param);
        }
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.ptzCmd(param);
    }


    /**
     * 获取巡航轨迹列表
     */
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrackList(VideoNodeBaseParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.getCruiseTrackList(param);
    }

    /**
     * 获取指定巡航轨迹
     */
    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrack(CruiseTrackNodeParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.getCruiseTrack(param);
    }

    /**
     * 开始巡航轨迹
     */
    public DtoResult<Void> startCruiseTrack(CruiseTrackNodeParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.startCruiseTrack(param);
    }

    /**
     * 停止巡航轨迹
     */
    public DtoResult<Void> stopCruiseTrack(CruiseTrackNodeParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.stopCruiseTrack(param);
    }

    /**
     * 删除巡航轨迹
     */
    public DtoResult<Void> delCruiseTrack(CruiseTrackNodeParam param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(param.getDeviceEntity());
        return signalingService.delCruiseTrack(param);
    }

    /**
     * 保存或更新巡航轨迹
     */
    public DtoResult<SaveOrUpdateCruiseTrackResp> saveOrUpdateCruiseTrack(VideoNodeBaseParam videoNodeBaseParam, ConvSaveOrUpdateCruiseTrackReq param) {
        VideoSignalingService signalingService = getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity());
        return signalingService.saveOrUpdateCruiseTrack(videoNodeBaseParam, param);
    }


    /**
     * 画面翻转控制
     */
    public DtoResult<VlinkerConvergeFlipVideoResp> pictureFlipControl(PictureFlipControlParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).pictureFlipControl(param);
    }

    /**
     * 获取画面翻转参数
     */
    public DtoResult<VlinkerConvergeFlipVideoResp> getPictureFlipParam(PictureFlipControlParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getPictureFlipParam(param);
    }


    /**
     * 获取灯光配置
     */
    public DtoResult<CommonSetStatusLightResp> getLightingArg(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getLightingArg(param);
    }

    /**
     * 设备重启
     */
    public DtoResult<Void> deviceRestart(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).deviceRestart(param);
    }

    /**
     * 设备重置
     */
    public DtoResult<Void> deviceReset(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).deviceReset(param);
    }

    /**
     * 设置补光灯
     */
    public DtoResult<VlinkerConvergeFillLightStatusResp> setFillLightingArg(SetFillLightingArgParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setFillLightingArg(param);
    }

    /**
     * 停止录像回放
     */
    public DtoResult<Void> stopPlayback(VideoNodeBaseParam param, CommonStopPlayBackReq playBackReq) {
        return getAccessWayTypeByStream(param.getDeviceEntity()).stopPlayback(param, playBackReq);
    }


    /**
     * 获取SD卡容量信息
     */
    public DtoResult<ConvergeSdCardCapacityResp> getSdCardCapacity(VideoNodeBaseParam videoNodeBaseParam) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).getSdCardCapacity(videoNodeBaseParam);
    }

    /**
     * SD卡格式化
     */
    public DtoResult<Void> sdCardFormatting(VideoNodeBaseParam videoNodeBaseParam) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).sdCardFormatting(videoNodeBaseParam);
    }

    /**
     * 获取报警参数
     */
    public DtoResult<CommonGetSideAlgorithmResp> getAlarmParameter(VideoNodeBaseParam videoNodeBaseParam, CommonGetSideAlgorithmReq param) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).getAlarmParameter(videoNodeBaseParam, param);
    }

    /**
     * 设置报警参数
     */
    public DtoResult<CommonSetSideAlgorithmResp> setAlarmParameter(VideoNodeBaseParam videoNodeBaseParam, CommonSetSideAlgorithmReq param) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).setAlarmParameter(videoNodeBaseParam, param);
    }

    /**
     * 设备升级
     */
    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(VideoNodeBaseParam videoNodeBaseParam, CommonDeviceUpgradeReq deviceUpgradeReq) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).deviceUpgrade(videoNodeBaseParam, deviceUpgradeReq);
    }

    /**
     * 获取OSD信息
     */
    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(VideoNodeBaseParam videoNodeBaseParam) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).getOsdInfo(videoNodeBaseParam);
    }


    /**
     * 视频通话
     */
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(VideoNodeBaseParam videoNodeBaseParam, CommonCallDeviceByVideoReq req) {
        return getAccessWayTypeByStream(videoNodeBaseParam.getDeviceEntity()).callDeviceByVideo(videoNodeBaseParam, req);
    }

    /**
     * 设置OSD信息
     */
    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(VideoNodeBaseParam videoNodeBaseParam, CommonSetOsdInfoReq commonSetOsdInfoReq) {
        return getAccessWayTypeForSignaling(videoNodeBaseParam.getDeviceEntity()).setOsdInfo(videoNodeBaseParam, commonSetOsdInfoReq);
    }

    /**
     * 设置云存录像计划
     */
    public DtoResult<Void> setCloudPlaybackPlan(PlaybackPlanParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setCloudPlaybackPlan(param);
    }

    /**
     * 查询通话状态
     */
    public DtoResult<Boolean> getAvCallStatus(VideoNodeBaseParam param, CommonGetAvCallStatusReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getAvCallStatus(param, req);
    }

    /**
     * 获取日志下载地址
     */
    public DtoResult<LogUriVo> getLogDownloadUrl(LogDownloadUrlParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getLogDownloadUrl(param);
    }

    /**
     * 获取休眠参数
     */
    public DtoResult<CommonGetDormancyResp> getDormancyParameter(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getDormancyParameter(param);
    }

    /**
     * 设置休眠参数
     */
    public DtoResult<CommonSetDormancyResp> setDormancyParameter(VideoNodeBaseParam param, CommonSetDormancyReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setDormancyParameter(param, req);
    }

    /**
     * 设置双摄联动
     */
    public DtoResult<Void> setDualCameraLinkage(VideoNodeBaseParam param, CommonSetDualCameraLinkageReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setDualCameraLinkage(param, req);
    }

    /**
     * 设置声光震慑
     */
    public DtoResult<Void> setSoundAndLightShock(VideoNodeBaseParam param, CommonSetSoundAndLightShockReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setSoundAndLightShock(param, req);
    }

    /**
     * 设置声光震慑
     */
    public DtoResult<Void> setSoundAndLightShockByFile(VideoNodeBaseParam param, CommonSetSoundAndLightShockReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setSoundAndLightShockByFile(param, req);
    }

    /**
     * 获取声光震慑配置
     */
    public DtoResult<CommonGetSoundAndLightShockReq> getSoundAndLightShock(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getSoundAndLightShock(param);
    }

    /**
     * 一键巡视
     */
    public DtoResult<Void> oneClickPatrol(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).oneClickPatrol(param);
    }

    /**
     * 设置人形标记
     */
    public DtoResult<Void> setHumanoidMarkers(VideoNodeBaseParam param, CommonSetHumanoidMarkersReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setHumanoidMarkers(param, req);
    }

    /**
     * 获取人形标记配置
     */
    public DtoResult<CommonGetHumanoidMarkersReq> getHumanoidMarkers(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getHumanoidMarkers(param);
    }

    /**
     * 云台校准
     */
    public DtoResult<Void> gimbalCalibration(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).gimbalCalibration(param);
    }


    /**
     * 根据xyz 跳转到对应的画面
     */
    public DtoResult<Void> setDevicRelativeXyz(VideoNodeBaseParam param, CommonSetDevicRelativeXyzReq req) {
        DeviceSdkAccessService accessWayTypeSdk = getAccessWayTypeSdk(param.getDeviceEntity());
        if (accessWayTypeSdk != null) {
            return accessWayTypeSdk.setDevicRelativeXyz(param, req);
        }
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setDevicRelativeXyz(param, req);
    }

    // 新增的信令操作方法，使用getAccessWayTypeForSignaling进行协议判断

    /**
     * 获取云台状态
     */
    public DtoResult<GimbalStatusVo> getGimbalStatus(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getGimbalStatus(param);
    }

    /**
     * 云台锁定
     */
    public DtoResult<Void> gimbalLock(GimbalLockParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).gimbalLock(param);
    }

    /**
     * 设备控制
     */
    public DtoResult<Void> deviceControl(DeviceControlParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).deviceControl(param);
    }

    /**
     * 云端语音对讲
     */
    public DtoResult<Void> cloudVoice(CloudVoiceParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).cloudVoice(param);
    }

    /**
     * 停止云端语音对讲
     */
    public DtoResult<Void> cloudVoiceStop(CloudVoiceParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).cloudVoiceStop(param);
    }

    /**
     * 获取PTZ精确参数
     */
    public Result getPtzPrecise(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getPtzPrecise(param);
    }

    /**
     * 控制巡航轨迹
     */
    public DtoResult<Void> ctrlCruiseTrack(CtrlCruiseTrackParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).ctrlCruiseTrack(param);
    }

    /**
     * 控制全景巡航
     */
    public DtoResult<Void> ctrlPanoramicCruise(CtrlPanoramicCruiseParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).ctrlPanoramicCruise(param);
    }

    /**
     * 获取全景巡航配置
     */
    public DtoResult<PanoramicCruiseVo> getPanoramicCruise(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getPanoramicCruise(param);
    }

    /**
     * 获取视频参数
     */
    public DtoResult<ConvergeVideoResp> getVideoParameter(GetVideoParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getVideoParameter(param);
    }

    /**
     * 设置视频参数
     */
    public DtoResult<Void> setVideoParameter(SetVideoParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setVideoParameter(param);
    }

    /**
     * 获取音频参数
     */
    public DtoResult<AudioParameterVo> getAudioParameter(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getAudioParameter(param);
    }

    /**
     * 设置音频参数
     */
    public DtoResult<Void> setAudioParameter(SetAudioParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setAudioParameter(param);
    }

    /**
     * 获取OSD参数
     */
    public DtoResult<OsdParameterVo> getOsdParameter(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getOsdParameter(param);
    }

    /**
     * 设置OSD参数
     */
    public DtoResult<Void> setOsdParameter(SetOsdParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setOsdParameter(param);
    }

    /**
     * 设置OSD时间参数
     */
    public DtoResult<Void> setOsdTimeParameter(SetOsdParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setOsdTimeParameter(param);
    }

    /**
     * 获取音量命令
     */
    public DtoResult<CommonGetVolumeCommandResp> getVolumeCommand(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getVolumeCommand(param);
    }

    /**
     * 设置音量命令
     */
    public DtoResult<Void> setVolumeCommand(VideoNodeBaseParam param, CommonSetVolumeCommandReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setVolumeCommand(param, req);
    }

    /**
     * 设置灯光参数
     */
    public DtoResult<ConvergeLightingArgResp> setLightingArg(SetLightingArgParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setLightingArg(param);
    }

    /**
     * NTP时间同步
     */
    public DtoResult<SdCardCapacityVo> ntpTiming(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).ntpTiming(param);
    }

    /**
     * 获取日志URI
     */
    public DtoResult<LogUriVo> getLogUri(VideoNodeBaseParam param, String httpUrl) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getLogUri(param, httpUrl);
    }

    /**
     * 设置录像计划
     */
    public DtoResult<Void> setPlaybackPlan(PlaybackPlanParamVideoNode param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setPlaybackPlan(param);
    }

    /**
     * 获取定时广播配置
     */
    public DtoResult<TimingBroadcastVo> getTimingBroadcast(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getTimingBroadcast(param);
    }

    /**
     * 获取屏幕信息
     */
    public DtoResult<CommonGetScreenPropertiesResp> getScreenInfo(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getScreenInfo(param);
    }

    /**
     * 设置屏幕信息
     */
    public DtoResult<Void> setScreenInfo(VideoNodeBaseParam param, CommonSetScreenPropertiesReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setScreenInfo(param, req);
    }

    /**
     * 读取设备文件
     */
    public DtoResult<CommonGetDeviceFileResp> readDeviceFiles(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).readDeviceFiles(param);
    }

    /**
     * 设置设备文件
     */
    public DtoResult<Void> setDeviceFile(VideoNodeBaseParam param, CommonSetFileReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setDeviceFile(param, req);
    }

    /**
     * 删除设备文件
     */
    public DtoResult<Void> delDeviceFile(VideoNodeBaseParam param, CommonDelFileReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).delDeviceFile(param, req);
    }

    /**
     * 获取设备能力开关信息
     */
    public DtoResult<CommonAbilityResp> getAbility(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getAbility(param);
    }

    /**
     * 设置设备能力开关
     */
    public DtoResult<Void> setAbility(VideoNodeBaseParam param, CommonAbilitySetReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setAbility(param, req);
    }

    /**
     * 获取通道能力开关信息
     */
    public DtoResult<CommonChannelAbilityResp> getChannelAbility(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getChannelAbility(param);
    }

    /**
     * 设置通道能力开关
     */
    public DtoResult<Void> setChannelAbility(VideoNodeBaseParam param, CommonChannelAbilitySetReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setChannelAbility(param, req);
    }

    /**
     * 读取动态的数据
     */
    public DtoResult<CommonDynamicDataResp> getDynamicData(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getDynamicData(param);
    }

    /**
     * 读取NTP地址
     */
    public DtoResult<CommonNtpResp> getNtp(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getNtp(param);
    }

    /**
     * 设置NTP地址
     */
    public DtoResult<Void> setNtp(VideoNodeBaseParam param, CommonNtpSetReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setNtp(param, req);
    }

    /**
     * 读取设备网卡信息
     */
    public DtoResult<CommonNetworkInterfaceResp> getNetworkInterface(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getNetworkInterface(param);
    }

    /**
     * 获取MTU
     */
    public DtoResult<CommonMtuResp> getMTU(VideoNodeBaseParam param, CommonGetMtuReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getMTU(param, req);
    }

    /**
     * 设置MTU
     */
    public DtoResult<Void> setMTU(VideoNodeBaseParam param, CommonSetMtuReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setMTU(param, req);
    }

    /**
     * 获取语音通话超时时间
     */
    public DtoResult<CommonVoiceTimeoutResp> getVoiceTimeout(VideoNodeBaseParam param) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).getVoiceTimeout(param);
    }

    /**
     * 设置语音通话超时时间
     */
    public DtoResult<Void> setVoiceTimeout(VideoNodeBaseParam param, CommonVoiceTimeoutSetReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).setVoiceTimeout(param, req);
    }

    /**
     * 语音状态通知
     */
    public DtoResult<Void> notifyVoiceStatus(VideoNodeBaseParam param, CommonVoiceStatusNotifyReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).notifyVoiceStatus(param, req);
    }

    /**
     * 设备订阅
     */
    public DtoResult<Void> deviceSubscribe(VideoNodeBaseParam param, CommonDeviceSubscribeReq req) {
        return getAccessWayTypeForSignaling(param.getDeviceEntity()).deviceSubscribe(param, req);
    }


}
