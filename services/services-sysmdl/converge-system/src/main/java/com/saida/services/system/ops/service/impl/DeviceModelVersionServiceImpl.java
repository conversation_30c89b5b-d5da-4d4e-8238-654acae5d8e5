package com.saida.services.system.ops.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.config.oss.OSSUtil;
import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.DeviceModelEntity;
import com.saida.services.converge.entity.DeviceModelVersionEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.deviceApi.req.CommonDeviceUpgradeReq;
import com.saida.services.deviceApi.resp.CommonGetNowDeviceVersionResp;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.dto.DeviceCapacityDto;
import com.saida.services.open.enums.DeviceCapacityEnum;
import com.saida.services.system.ops.mapper.DeviceMapper;
import com.saida.services.system.ops.mapper.DeviceModelVersionMapper;
import com.saida.services.system.ops.service.DeviceModelService;
import com.saida.services.system.ops.service.DeviceModelVersionService;
import com.saida.services.system.ops.service.SignalNodeService;
import com.saida.services.system.video.param.VideoNodeBaseParam;
import com.saida.services.system.video.service.VideoCommonService;
import com.saida.services.tools.attr.AttrUtil;
import groovy.lang.Lazy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("deviceModelVersionService")
public class DeviceModelVersionServiceImpl extends BaseServiceImpl<DeviceModelVersionMapper, DeviceModelVersionEntity> implements DeviceModelVersionService {

    @Lazy
    @Resource
    private DeviceModelService deviceModelService;

    @Lazy
    @Resource
    private DeviceMapper deviceMapper;

    @Value("${sysconfig.uploadPath:#{null}}")
    private String uploadPath;

    @Resource
    private OSSUtil ossUtil;

    @Lazy
    @Resource
    private SignalNodeService signalNodeService;

    /**
     * 添加或更新设备型号版本信息。
     *
     * @param entity 设备型号版本实体，包含版本相关信息。
     * @throws BizRuntimeException 如果操作失败，抛出业务运行时异常。
     */
    @Override
    public void addOrUpdate(DeviceModelVersionEntity entity) {
        // 替换文件下载路径中的主机和前缀为上传路径
        String filePath = entity.getUrl().replace(String.format("%s/%s", FileService.HOST, FileService.PREFIX), uploadPath + FileService.SEPARATOR);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // 下载文件至输出流
            HttpUtil.download(filePath, outputStream, true);
        } catch (Exception e) {
            // 如果下载失败，抛出异常表示安装包不存在
            throw new BizRuntimeException("安装包不存在");
        }

        // 根据型号ID查询设备型号信息
        DeviceModelEntity model = deviceModelService.getInfo(entity.getModelId());
        if (model == null) {
            // 如果型号信息不存在，抛出异常
            throw new BizRuntimeException("设备型号不存在");
        }
        entity.setModelStr(model.getModel());

        // 解析包名获取文件名和后缀
        int begin = entity.getPackageName().lastIndexOf(".");
        //获得文件名不含后缀
        String fileName = entity.getPackageName().substring(0, begin);
        //获得文件后缀名
        String fileType = entity.getPackageName().substring(begin);

        if (entity.getId() == null) { // 新增版本
            // 根据型号ID和版本号查询已存在的版本信息
            DeviceModelVersionEntity version = getOne(
                    new LambdaQueryWrapper<DeviceModelVersionEntity>()
                            .eq(DeviceModelVersionEntity::getModelId, entity.getModelId())
                            .eq(DeviceModelVersionEntity::getNumLong, entity.getNumLong())
                    , false);
            if (version != null) {
                // 如果已存在相同版本号，抛出异常
                throw new BizRuntimeException("版本号已存在");
            }

            // 设置创建用户ID和时间
            entity.setCreateUser(JwtUtil.getUserId());
            entity.setCreateTime(DateTime.now());

            // 生成OSS对象键
            // fileName-date.fileType
            String ossObjKey = String.format("%s-%s%s", fileName, DateTime.now().toString("yyyyMMddHHmmss"), fileType);
            InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 上传文件至OSS
            ossUtil.upload(inputStream, (long) outputStream.size(), ossObjKey);
            entity.setOssObjKey(ossObjKey);

            // 查询最后的一个版本号
            DeviceModelVersionEntity lastVersion = this.getOne(
                    new LambdaQueryWrapper<DeviceModelVersionEntity>()
                            .eq(DeviceModelVersionEntity::getModelId, entity.getModelId())
                            .orderByDesc(DeviceModelVersionEntity::getNumLong)
                            .last(" limit 1 "));
            if (lastVersion != null) {
                log.info("addOrUpdate lastVersion:{}", lastVersion);
                entity.setResolvingPower(lastVersion.getResolvingPower());
                entity.setBitRate(lastVersion.getBitRate());
                entity.setFrameRate(lastVersion.getFrameRate());
                entity.setEncodingFormat(lastVersion.getEncodingFormat());
                entity.setAudioFormat(lastVersion.getAudioFormat());
                entity.setVideoAlarm(lastVersion.getVideoAlarm());
                entity.setDeviceAlarm(lastVersion.getDeviceAlarm());
                entity.setGpsAlarm(lastVersion.getGpsAlarm());
                entity.setFaultAlarm(lastVersion.getFaultAlarm());
                entity.setDeviceCapacity(lastVersion.getDeviceCapacity());
                entity.setDeviceBtnDic(lastVersion.getDeviceBtnDic());
            }
            // 保存版本信息
            save(entity);
        } else { // 更新版本
            // 根据型号ID和版本号（不包括当前ID）查询已存在的版本信息
            DeviceModelVersionEntity tmp = getOne(
                    new LambdaQueryWrapper<DeviceModelVersionEntity>()
                            .ne(DeviceModelVersionEntity::getId, entity.getId())
                            .eq(DeviceModelVersionEntity::getModelId, entity.getModelId())
                            .eq(DeviceModelVersionEntity::getNumLong, entity.getNumLong())
                    , false);
            if (tmp != null) {
                // 如果已存在相同版本号，抛出异常
                throw new BizRuntimeException("版本号已存在");
            }

            // 设置更新用户ID和时间
            entity.setUpdateUser(JwtUtil.getUserId());
            entity.setUpdateTime(DateTime.now());

            // 根据ID查询当前版本信息
            DeviceModelVersionEntity version = getById(entity.getId());
            if (version == null) {
                // 如果当前版本信息不存在，抛出异常
                throw new BizRuntimeException("版本不存在");
            }
            // 如果URL已改变，重新上传文件至OSS并更新OSS对象键
            if (!entity.getUrl().equals(version.getUrl())) {
                // fileName-date.fileType
                String ossObjKey = String.format("%s-%s%s", fileName, DateTime.now().toString("yyyyMMddHHmmss"), fileType);
                InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
                ossUtil.upload(inputStream, (long) outputStream.size(), ossObjKey);
                entity.setOssObjKey(ossObjKey);
            }
            // 更新版本信息
            updateById(entity);
        }
    }


    /**
     * 分页查询设备型号版本信息。
     * 本方法根据设备型号ID查询相应的设备型号版本，并支持分页和按发布日期降序排序。
     * 如果指定的设备型号不存在，则抛出业务运行异常。
     * 在查询结果中，会填充设备型号的额外属性信息。
     *
     * @param entity 包含设备型号ID、页码和页大小的设备型号版本实体。
     * @return 返回分页查询结果，包含设备型号版本信息。
     */
    @Override
    public IPage<DeviceModelVersionEntity> listPage(DeviceModelVersionEntity entity) {
        // 根据设备型号ID查询设备型号信息
        DeviceModelEntity model = deviceModelService.getInfo(entity.getModelId());
        // 如果设备型号不存在，则抛出异常
        if (model == null) {
            throw new BizRuntimeException("设备型号不存在");
        }
        // 进行分页查询，根据设备型号ID和发布日期降序排序
        IPage<DeviceModelVersionEntity> page = page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, entity.getModelId())
                        .orderByDesc(DeviceModelVersionEntity::getReleaseDate)
        );
        // 如果查询结果为空或没有记录，则直接返回查询结果
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        // 填充设备型号的额外属性信息到查询结果中
        fillAttr(model, page.getRecords());
        // 返回填充后的查询结果
        return page;
    }


    /**
     * 根据设备型号ID获取设备型号版本列表。
     * 该方法首先通过设备型号ID查询设备型号信息，若型号不存在则抛出异常。
     * 接着查询该型号的所有版本信息，并按发布日期降序排序。
     * 如果查询结果为空，直接返回空列表。
     * 否则，将型号的属性填充到每个版本实体中，然后返回版本列表。
     *
     * @param entity 包含设备型号ID的实体对象。
     * @return 设备型号版本列表，按发布日期降序排序。
     * @throws BizRuntimeException 如果设备型号不存在，则抛出此异常。
     */
    @Override
    public List<DeviceModelVersionEntity> getList(DeviceModelVersionEntity entity) {
        // 根据设备型号ID查询设备型号信息
        DeviceModelEntity model = deviceModelService.getInfo(entity.getModelId());
        // 如果设备型号不存在，则抛出异常
        if (model == null) {
            throw new BizRuntimeException("设备型号不存在");
        }
        // 查询该型号的所有版本信息，并按发布日期降序排序
        List<DeviceModelVersionEntity> records = list(
                new LambdaQueryWrapper<DeviceModelVersionEntity>().eq(DeviceModelVersionEntity::getModelId, entity.getModelId())
                        .orderByDesc(DeviceModelVersionEntity::getReleaseDate)
        );
        // 如果查询结果为空，直接返回空列表
        if (records == null || records.isEmpty()) {
            return records;
        }
        // 将型号的属性填充到每个版本实体中
        fillAttr(model, records);
        // 返回填充后的版本列表
        return records;
    }


    /**
     * 根据ID获取设备型号版本信息。
     *
     * @param id 设备型号版本的唯一标识ID。
     * @return 返回对应的设备型号版本实体对象。
     * @throws BizRuntimeException 如果设备型号不存在，则抛出业务运行时异常。
     */
    @Override
    public DeviceModelVersionEntity getInfo(Long id) {
        // 根据ID查询设备型号版本信息
        DeviceModelVersionEntity version = this.getById(id);
        // 如果版本信息不存在，则直接返回null
        if (version == null) {
            return null;
        }
        // 通过版本中的模型ID查询设备型号信息
        DeviceModelEntity model = deviceModelService.getInfo(version.getModelId());
        // 如果设备型号信息不存在，则抛出异常
        if (model == null) {
            throw new BizRuntimeException("设备型号不存在");
        }
        // 填充设备型号属性到版本信息中，并返回填充后的版本信息
        return fillAttr(model, new ArrayList<DeviceModelVersionEntity>() {{
            add(version);
        }}).get(0);
    }

    /**
     * 根据设备ID获取设备型号版本信息。
     * 此方法通过设备ID查找对应的设备，然后根据设备的型号查找设备型号版本，最终返回设备型号版本实体。
     * 如果设备不存在或设备型号不存在，将抛出BizRuntimeException异常。
     *
     * @param id 设备ID，用于查找特定设备。
     * @return DeviceModelVersionEntity 设备型号版本实体，包含设备型号和版本信息。
     */
    @Override
    public DeviceModelVersionEntity getInfoByDeviceId(Long id) {
        // 根据设备ID查询设备信息
        DeviceEntity device = deviceMapper.selectById(id);
        // 检查设备是否存在，如果不存在则抛出异常
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        if (StringUtil.isEmpty(device.getModel())) {
            DeviceModelVersionEntity temp = new DeviceModelVersionEntity();
            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                temp.addDeviceCapacity(byField);
            });
            return temp;
        }
        // 根据设备型号查询设备型号信息
        DeviceModelEntity model = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(DeviceModelEntity::getModel, device.getModel()), false);
        if (model == null) {
            DeviceModelVersionEntity temp = new DeviceModelVersionEntity();
            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                temp.addDeviceCapacity(byField);
            });
            return temp;
        }
        // 根据设备型号ID和设备版本号查询设备型号版本信息
        DeviceModelVersionEntity one = super.getOne(
                new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, model.getId())
                        .eq(DeviceModelVersionEntity::getVersionNum, device.getVersion()), false);
        if (one == null) {
            DeviceModelVersionEntity temp = new DeviceModelVersionEntity();
            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                temp.addDeviceCapacity(byField);
            });
            return temp;
        }
        return one;
    }

    @Override
    public void delete(Long id) {
        removeById(id);
    }

    /**
     * 根据设备型号和版本号获取下载链接。
     * <p>
     * 此方法通过设备型号和版本号查询对应的设备型号信息和版本信息，然后根据版本的OSS对象键生成下载链接。
     * 如果找不到对应的设备型号或版本，将抛出BizRuntimeException异常。
     *
     * @param model      设备型号。
     * @param versionNum 版本号。
     * @return 对应版本的下载链接。
     * @throws BizRuntimeException 如果设备型号或版本不存在，则抛出此异常。
     */
    @Override
    public String getDownloadUrl(String model, String versionNum) {
        // 根据设备型号查询设备型号实体
        DeviceModelEntity deviceModel = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>().eq(DeviceModelEntity::getModel, model), false);
        // 如果设备型号不存在，则抛出异常
        if (deviceModel == null) {
            throw new BizRuntimeException("设备型号不存在");
        }
        // 根据设备型号ID和版本号查询版本实体
        DeviceModelVersionEntity version = getOne(
                new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, deviceModel.getId())
                        .eq(DeviceModelVersionEntity::getVersionNum, versionNum)
                , false
        );
        // 如果版本不存在，则抛出异常
        if (version == null) {
            throw new BizRuntimeException("版本不存在");
        }
        // 通过OSS工具类和版本的OSS对象键生成并返回下载链接
        return ossUtil.downloadUrl(version.getOssObjKey());
    }

    @Override
    public String getDownloadUrl(Long id) {
        // 根据设备型号ID和版本号查询版本实体
        DeviceModelVersionEntity version = super.getById(id);
        // 如果版本不存在，则抛出异常
        if (version == null) {
            throw new BizRuntimeException("版本不存在");
        }
        // 通过OSS工具类和版本的OSS对象键生成并返回下载链接
        return ossUtil.downloadUrl(version.getOssObjKey());
    }

    @Resource
    private VideoCommonService videoCommonService;

    /**
     * 批量更新设备版本信息。
     *
     * @param versionId 版本ID，用于查询特定版本信息。
     * @param devIds    设备ID字符串，多个ID使用逗号分隔。
     * @return 更新结果，成功返回空字符串，失败返回错误信息。
     * @throws BizRuntimeException 当版本、设备、设备型号不存在或设备型号不匹配、设备已是最新版本、设备未绑定节点、升级失败时，抛出此异常。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateDevice(Long versionId, String devIds) {
        // 根据版本ID查询版本信息
        DeviceModelVersionEntity version = getById(versionId);
        // 如果版本信息不存在，抛出业务运行时异常
        if (version == null) {
            throw new BizRuntimeException("版本不存在");
        }
        // 将设备ID字符串转换为Long类型的列表，去除重复项
        List<Long> devIdList = Arrays.stream(devIds.split(","))
                .filter(NumberUtil::isNumber)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        // 如果设备ID列表为空，抛出业务运行时异常
        if (devIdList.isEmpty()) {
            throw new BizRuntimeException("设备必选");
        }
        // 根据设备ID列表查询设备信息
        List<DeviceEntity> deviceList = deviceMapper.selectBatchIds(devIdList);
        // 如果设备信息为空或列表为空，抛出业务运行时异常
        if (deviceList == null || deviceList.isEmpty()) {
            throw new BizRuntimeException("设备不存在");
        }
        // 根据版本的型号ID查询型号信息
        DeviceModelEntity model = deviceModelService.getById(version.getModelId());
        // 如果型号信息不存在，抛出业务运行时异常
        if (model == null) {
            throw new BizRuntimeException("版本型号不存在");
        }
        // 获取版本的下载URL
        String downloadUrl = version.getUrl();
        // 用于记录升级过程中的错误信息
        StringBuilder errMsg = new StringBuilder();

        CommonDeviceUpgradeReq req = new CommonDeviceUpgradeReq();
        req.setFileUrl(downloadUrl);
        req.setVersionNum(version.getVersionNum());
        req.setFileSize(version.getFileSize());
        req.setFileMd5(version.getMd5());
        // 遍历设备列表，进行升级操作
        for (DeviceEntity d : deviceList) {
            // 如果设备型号与版本型号不匹配，记录错误信息并跳过当前设备
            if (!StrUtil.equals(model.getModel(), d.getModel())) {
                errMsg.append(String.format("设备：%s 型号不匹配升级失败\n", d.getName()));
                continue;
            }
            // 如果设备已是最新版本，记录错误信息并跳过当前设备
            if (StrUtil.equals(version.getVersionNum(), d.getVersion())) {
                errMsg.append(String.format("设备：%s 已是最新版本\n", d.getName()));
                continue;
            }
            // 根据设备ID查询信号节点信息
            SignalNodeEntity node = signalNodeService.getById(d.getNodeId());
            if (node == null) {
                errMsg.append(String.format("设备：%s 未绑定节点升级失败\n", d.getName()));
                continue;
            }
            try {
                VideoNodeBaseParam videoNodeBaseParam = new VideoNodeBaseParam();
                videoNodeBaseParam.setDeviceId(d.getId());
                videoNodeBaseParam.setDeviceCode(d.getDeviceCode());
                videoNodeBaseParam.setDeviceEntity(d);
                DtoResult<CommonGetNowDeviceVersionResp> dtoResult = videoCommonService.deviceUpgrade(videoNodeBaseParam, req);
                log.info("deviceUpgrade => dtoResult:{}", dtoResult);
//                CommonSetFileReq setFileReq = new CommonSetFileReq();
//                setFileReq.setFileUrl(req.getFileUrl());
//                setFileReq.setFileHash(req.getFileMd5());
//                setFileReq.setFileSize(req.getFileSize());
//                setFileReq.setStoragePrefer(new byte[]{2, 1, 0});
//                setFileReq.setFileTag("deviceUpgrade");
//                videoCommonService.setDeviceFile(videoNodeBaseParam, setFileReq);
            } catch (Exception e) {
                // 记录升级过程中的异常信息
                log.error("记录升级过程中的异常信息 {}", e.getMessage(), e);
                errMsg.append(String.format("设备：%s 升级失败，错误：%s\n", d.getName(), e.getMessage()));
            }
        }
        // 如果没有错误信息，返回成功结果
        if (StringUtil.isEmpty(errMsg.toString())) {
            return Result.ok();
        }
        // 如果有错误信息，返回错误结果，包含所有错误信息
        return Result.error(errMsg.toString());
    }

    @Override
    public DeviceModelVersionEntity getInfoByModelAndVersion(String model, String version) {
        return getBaseMapper().getInfoByModelAndVersion(model, version);
    }


    /**
     * 填充设备型号版本信息的属性。
     *
     * @param model   设备型号实体，用于获取制造商名称和型号信息。
     * @param records 设备型号版本实体列表，需要填充属性。
     * @return 填充完属性的设备型号版本实体列表。
     * <p>
     * 此方法主要用于将设备型号的制造商名称和型号信息填充到设备型号版本的信息中。
     * 如果传入的版本记录列表为空或为空列表，则直接返回原列表。
     * 通过将型号实体转换为JSON字符串，再解析出制造商名称，以确保属性填充的准确性。
     * 使用HashMap来存储型号ID和对应的制造商名称、型号信息，以便于后续的属性填充。
     * 最后，遍历版本记录列表，为每个版本实体填充制造商名称和型号属性。
     */
    private List<DeviceModelVersionEntity> fillAttr(DeviceModelEntity model, List<DeviceModelVersionEntity> records) {
        if (records == null || records.isEmpty()) {
            return records;
        }
        String manufactorName = JSON.parseObject(JSON.toJSONString(model)).getString("manufactorName");
        Map<Object, Object> dicMap = new HashMap<>();
        dicMap.put(model.getId(), new HashMap<String, String>() {{
            put("manufactorName", manufactorName);
            put("modelName", model.getModel());
        }});
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }


    public DeviceModelEntity buildDeviceVersion(String modelStr, String version) {
        if (StringUtil.isNotEmpty(modelStr)) {
            try {
                DeviceModelEntity model = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                        .eq(DeviceModelEntity::getModel, modelStr).last(" limit 1"));
                Long modelId;
                if (model != null) {
                    modelId = model.getId();
                } else {
                    DeviceModelEntity entity = new DeviceModelEntity();
                    entity.setModel(modelStr);
                    entity.setDeviceType(1L);
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    deviceModelService.save(entity);
                    modelId = entity.getId();
                }
                if (StringUtil.isNotEmpty(version)) {
                    DeviceModelVersionEntity nowVersion = this.getOne(
                            new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                    .eq(DeviceModelVersionEntity::getModelId, modelId)
                                    .eq(DeviceModelVersionEntity::getVersionNum, version));
                    // 版本不存在 构建一个版本出来
                    if (nowVersion == null) {
                        nowVersion = new DeviceModelVersionEntity();
                        nowVersion.setModelId(modelId);
                        nowVersion.setModelStr(modelStr);
                        nowVersion.setVersionNum(version);
                        nowVersion.setMd5(version);
                        nowVersion.setReleaseDate(new Date());
                        nowVersion.setCreateTime(new Date());
                        nowVersion.setUpdateTime(new Date());
                        // 查询最后的一个版本号
                        DeviceModelVersionEntity lastVersion = this.getOne(
                                new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                        .eq(DeviceModelVersionEntity::getModelId, modelId)
                                        .orderByDesc(DeviceModelVersionEntity::getNumLong)
                                        .last(" limit 1 "));
                        if (lastVersion != null) {
                            log.info("buildDeviceVersion lastVersion:{}", lastVersion);
                            nowVersion.setNumLong(lastVersion.getNumLong() == null ? 1 : lastVersion.getNumLong() + 1);
                            nowVersion.setResolvingPower(lastVersion.getResolvingPower());
                            nowVersion.setBitRate(lastVersion.getBitRate());
                            nowVersion.setFrameRate(lastVersion.getFrameRate());
                            nowVersion.setEncodingFormat(lastVersion.getEncodingFormat());
                            nowVersion.setAudioFormat(lastVersion.getAudioFormat());
                            nowVersion.setVideoAlarm(lastVersion.getVideoAlarm());
                            nowVersion.setDeviceAlarm(lastVersion.getDeviceAlarm());
                            nowVersion.setGpsAlarm(lastVersion.getGpsAlarm());
                            nowVersion.setFaultAlarm(lastVersion.getFaultAlarm());
                            nowVersion.setDeviceCapacity(lastVersion.getDeviceCapacity());
                            nowVersion.setDeviceBtnDic(lastVersion.getDeviceBtnDic());
                        }else {
                            nowVersion.setNumLong(1L);
                            Map<String, DeviceCapacityDto> capacityDtoMap = new HashMap<>();
                            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                                DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                                capacityDtoMap.put(e.getField(), byField);
                            });
                            nowVersion.setDeviceCapacity(capacityDtoMap);
                        }
                        this.save(nowVersion);
                    }
                }
                return model;
            } catch (Exception e) {
                log.error("可能会因为线程安全问题导致model 重复  如果触发了唯一键冲突 不用理会", e);
            }
        }
        return null;
    }
}