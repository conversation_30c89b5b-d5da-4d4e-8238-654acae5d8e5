package com.saida.services.system.sys.dto;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

public class SDKMessageFileDownloadToDeviceRequest {
    // 1 SD卡 2 临时 4 持久化
    public byte[] storagePrefer = new byte[3];     // length = 3
    public byte retryTimes = 5;
    public int fileSize;
    public byte[] fileHash = new byte[SDKMessageFileConstants.SHA_DIGEST_LENGTH]; // 20 bytes
    public String fileDownloadTaskTrackToken;
    public String fileDownloadURL;
    public String fileNameToStore;
    public int fileDownloadTimeoutInSeconds = 60 * 5;

    @Override
    public String toString() {
        return "SDKMessageFileDownloadToDeviceRequest{" +
                "storagePrefer=" + Arrays.toString(storagePrefer) +
                ", retryTimes=" + retryTimes +
                ", fileSize=" + fileSize +
                ", fileHash=" + Arrays.toString(fileHash) +
                ", fileDownloadTaskTrackToken='" + fileDownloadTaskTrackToken + '\'' +
                ", fileDownloadURL='" + fileDownloadURL + '\'' +
                ", fileNameToStore='" + fileNameToStore + '\'' +
                ", fileDownloadTimeoutInSeconds=" + fileDownloadTimeoutInSeconds +
                '}';
    }

    public byte[] toBytes() {
        byte[] tokenBytes = fileDownloadTaskTrackToken.getBytes(StandardCharsets.UTF_8);
        byte[] urlBytes = fileDownloadURL.getBytes(StandardCharsets.UTF_8);
        byte[] nameBytes = fileNameToStore.getBytes(StandardCharsets.UTF_8);

        int totalLength = 44 + tokenBytes.length + urlBytes.length + nameBytes.length;
        ByteBuffer buffer = ByteBuffer.allocate(totalLength);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        // header
        buffer.put(storagePrefer);                              // 3 bytes
        buffer.put(retryTimes);                                 // 1 byte
        buffer.putInt(fileSize);                                // 4 bytes
        buffer.put(fileHash);                                   // 20 bytes
        buffer.putInt(tokenBytes.length);                       // 4 bytes
        buffer.putInt(urlBytes.length);                         // 4 bytes
        buffer.putInt(nameBytes.length);                        // 4 bytes
        buffer.putInt(fileDownloadTimeoutInSeconds);            // 4 bytes

        // payload
        buffer.put(tokenBytes);
        buffer.put(urlBytes);
        buffer.put(nameBytes);

        return buffer.array();
    }
}

