package com.saida.services.system.analyse.controller;

import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.system.analyse.service.TaskDispatchGrpcStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/taskDispatchGrpc")
public class TaskDispatchGrpcStatusController {

    @Resource
    private TaskDispatchGrpcStatusService taskDispatchGrpcStatusService;

    @GetMapping("getNodeGrpcDto")
    public Result getNodeGrpcDto(String nodeId) {
        if (StringUtil.isEmpty(nodeId)) {
            return Result.error("nodeId不能为空");
        }
        return taskDispatchGrpcStatusService.getNodeGrpcDto(nodeId);
    }


}