package com.saida.services.system.video.indexer;

import com.saida.services.system.video.algHall.saidaPlayer.Message;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;

/**
 * Parser for SAIDA media indexer .index files (little-endian).
 * Uses Message.Header (12 bytes) where contentLength indicates body size.
 * Recognizes bodies:
 * - 20 bytes => MediaIndexerDataBufferInfo (bitmap,u64 begin,u64 end)
 * - 4 bytes  => MediaIndexerKeyFrameInfo (pos)
 */
public class IndexFileParser {

    public static class ParseResult {
        public final List<MediaIndexerDataBufferInfo> buffers;
        public final List<MediaIndexerKeyFrameInfo> keyFrames;
        public ParseResult(List<MediaIndexerDataBufferInfo> buffers, List<MediaIndexerKeyFrameInfo> keyFrames) {
            this.buffers = buffers;
            this.keyFrames = keyFrames;
        }
    }

    public ParseResult parse(byte[] data) {
        List<MediaIndexerDataBufferInfo> buffers = new ArrayList<>();
        List<MediaIndexerKeyFrameInfo> keyFrames = new ArrayList<>();

        int pos = 0;
        int limit = data.length;
        while (pos + 12 <= limit) {
            // read potential header
            byte[] headerBytes = new byte[12];
            System.arraycopy(data, pos, headerBytes, 0, 12);
            // check sync byte 0xE1
            if (headerBytes[0] != (byte) 0xE1) {
                pos += 1; // try to resync
                continue;
            }
            Message.Header header = Message.Header.newHeader(headerBytes);
            int bodyLen = header.contentLength;
            int total = 12 + bodyLen;
            if (pos + total > limit) {
                break; // incomplete tail
            }
            // parse body
            ByteBuffer body = ByteBuffer.wrap(data, pos + 12, bodyLen).order(ByteOrder.LITTLE_ENDIAN);
            if (bodyLen == MediaIndexerDataBufferInfo.BODY_SIZE) {
                long bitmap = Integer.toUnsignedLong(body.getInt());
                long beginSec = body.getLong();
                long endSec = body.getLong();
                buffers.add(new MediaIndexerDataBufferInfo(header, bitmap, beginSec, endSec));
                pos += total;
            } else if (bodyLen == MediaIndexerKeyFrameInfo.BODY_SIZE) {
                long p = Integer.toUnsignedLong(body.getInt());
                keyFrames.add(new MediaIndexerKeyFrameInfo(header, p));
                pos += total;
            } else {
                // unknown message; skip it by total length
                pos += total;
            }
        }
        return new ParseResult(buffers, keyFrames);
    }
}
